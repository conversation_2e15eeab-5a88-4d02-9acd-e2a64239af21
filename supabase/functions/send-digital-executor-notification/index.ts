import { corsHeaders } from '../_shared/cors.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured')
    }

    const { executorEmail, executorName, userEmail, userName } = await req.json()

    if (!executorEmail || !executorName || !userEmail || !userName) {
      throw new Error('Missing required parameters')
    }

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You've been designated as Digital Executor</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: #ffffff; 
            }
            .header { 
              background: linear-gradient(135deg, #f59e0b, #d97706); 
              color: white; 
              padding: 30px 20px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 24px; 
              font-weight: 600; 
            }
            .content { 
              padding: 30px 20px; 
              background: #ffffff; 
            }
            .content h2 { 
              color: #1f2937; 
              margin-top: 0; 
            }
            .highlight-box { 
              background: #fef3c7; 
              border-left: 4px solid #f59e0b; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 0 8px 8px 0; 
            }
            .responsibility-box { 
              background: #f0f9ff; 
              border: 1px solid #0ea5e9; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 8px; 
            }
            .responsibility-box h3 { 
              color: #0369a1; 
              margin-top: 0; 
            }
            .button { 
              display: inline-block; 
              background: #f59e0b; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 20px 0; 
              font-weight: 500; 
            }
            .footer { 
              padding: 20px; 
              text-align: center; 
              color: #6b7280; 
              font-size: 14px; 
              background: #f9fafb; 
            }
            ul { 
              padding-left: 20px; 
            }
            li { 
              margin: 8px 0; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>👑 Volnt - Digital Executor</h1>
            </div>
            <div class="content">
              <h2>Hello ${executorName},</h2>
              
              <div class="highlight-box">
                <p><strong>${userName}</strong> has designated you as their <strong>Digital Executor</strong> in their digital will and testament on Volnt.</p>
              </div>
              
              <p>This is a position of significant trust and responsibility. As a Digital Executor, you will have the authority to manage and distribute ${userName}'s digital assets according to their wishes.</p>
              
              <div class="responsibility-box">
                <h3>🔑 Your Responsibilities as Digital Executor</h3>
                <ul>
                  <li><strong>Asset Management:</strong> Access and manage all digital accounts and assets</li>
                  <li><strong>Distribution:</strong> Distribute digital assets to beneficiaries as specified in the will</li>
                  <li><strong>Security:</strong> Handle password managers, encrypted data, and access credentials</li>
                  <li><strong>Coordination:</strong> Work with other executors, beneficiaries, and legal representatives</li>
                  <li><strong>Documentation:</strong> Maintain records of all digital asset transfers and distributions</li>
                </ul>
              </div>
              
              <h3>Important Notes:</h3>
              <ul>
                <li><strong>Authority:</strong> You have full authority to access and manage digital assets, but cannot use them for personal benefit</li>
                <li><strong>Scope:</strong> Your role is limited to digital assets - you can view asset information but cannot access passwords or sensitive data unless specifically granted</li>
                <li><strong>Beneficiaries:</strong> Only designated beneficiaries can access the actual assets assigned to them</li>
                <li><strong>Legal Duty:</strong> You have a fiduciary duty to act in the best interests of the estate and beneficiaries</li>
              </ul>
              
              <h3>What Happens Next:</h3>
              <ul>
                <li><strong>Familiarize yourself</strong> with ${userName}'s digital assets and their distribution plan</li>
                <li><strong>Keep this email</strong> as proof of your designation</li>
                <li><strong>Stay in contact</strong> with ${userName} about any changes to their digital estate</li>
                <li><strong>Consider legal counsel</strong> if you have questions about your duties and responsibilities</li>
              </ul>
              
              <p>If you have any questions about this role, need clarification about your responsibilities, or wish to decline this appointment, please contact ${userName} directly at <a href="mailto:${userEmail}">${userEmail}</a>.</p>
              
              <div style="text-align: center;">
                <a href="https://volnt.xyz" class="button">Learn More About Digital Executors</a>
              </div>
            </div>
            <div class="footer">
              <p>This email was sent by Volnt on behalf of ${userName}.</p>
              <p><strong>Volnt</strong> - Secure your digital legacy</p>
              <p>If you believe you received this email in error, please contact ${userName} directly.</p>
            </div>
          </div>
        </body>
      </html>
    `

    const emailData = {
      from: 'Volnt <<EMAIL>>',
      to: [executorEmail],
      subject: `You've been designated as Digital Executor by ${userName}`,
      html: emailHtml,
    }

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify(emailData),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Resend API error: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    
    return new Response(JSON.stringify({ 
      success: true, 
      messageId: result.id,
      message: 'Digital executor notification sent successfully' 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in send-digital-executor-notification:', error)
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || 'Failed to send digital executor notification' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})