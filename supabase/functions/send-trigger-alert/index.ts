import { corsHeaders } from '../_shared/cors.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured')
    }

    const { 
      recipients, 
      alertType, 
      userName, 
      userEmail, 
      daysInactive, 
      customMessage 
    } = await req.json()

    if (!recipients || !alertType || !userName || !userEmail) {
      throw new Error('Missing required parameters')
    }

    if (!Array.isArray(recipients) || recipients.length === 0) {
      throw new Error('Recipients must be a non-empty array')
    }

    // Generate email content based on alert type
    let subject = ''
    let emailHtml = ''

    if (alertType === 'inactivity_warning') {
      subject = `⚠️ Inactivity Alert: ${userName} - ${daysInactive || 'Unknown'} days`
      emailHtml = generateInactivityWarningEmail(userName, userEmail, daysInactive, customMessage)
    } else if (alertType === 'verification_request') {
      subject = `🔍 Verification Required: ${userName}'s Digital Will`
      emailHtml = generateVerificationRequestEmail(userName, userEmail, customMessage)
    } else {
      throw new Error(`Unknown alert type: ${alertType}`)
    }

    // Send emails to all recipients
    const emailPromises = recipients.map(async (recipient: any) => {
      if (!recipient.email || !recipient.name) {
        throw new Error('Each recipient must have email and name')
      }

      const emailData = {
        from: 'Volnt Alerts <<EMAIL>>',
        to: [recipient.email],
        subject: subject,
        html: emailHtml.replace(/\{RECIPIENT_NAME\}/g, recipient.name),
      }

      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify(emailData),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Resend API error for ${recipient.email}: ${response.status} - ${errorText}`)
      }

      return await response.json()
    })

    const results = await Promise.all(emailPromises)

    return new Response(JSON.stringify({ 
      success: true, 
      emailsSent: results.length,
      messageIds: results.map(r => r.id),
      message: `${alertType} alerts sent successfully to ${results.length} recipients`
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in send-trigger-alert:', error)
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || 'Failed to send trigger alert' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})

function generateInactivityWarningEmail(userName: string, userEmail: string, daysInactive: number, customMessage?: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Inactivity Alert</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: #ffffff; 
          }
          .header { 
            background: linear-gradient(135deg, #dc2626, #b91c1c); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
          }
          .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
          }
          .content { 
            padding: 30px 20px; 
            background: #ffffff; 
          }
          .alert-box { 
            background: #fef2f2; 
            border: 1px solid #dc2626; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
          }
          .alert-box h2 { 
            color: #dc2626; 
            margin-top: 0; 
          }
          .custom-message { 
            background: #f3f4f6; 
            border-left: 4px solid #6b7280; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0; 
          }
          .footer { 
            padding: 20px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            background: #f9fafb; 
          }
          ul { 
            padding-left: 20px; 
          }
          li { 
            margin: 8px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Volnt Inactivity Alert</h1>
          </div>
          <div class="content">
            <h2>Hello {RECIPIENT_NAME},</h2>
            
            <div class="alert-box">
              <h2>⚠️ Inactivity Detected</h2>
              <p><strong>${userName}</strong> (${userEmail}) has been inactive for <strong>${daysInactive} days</strong>.</p>
            </div>
            
            <h3>What this means:</h3>
            <ul>
              <li>${userName} has not logged into their Volnt account recently</li>
              <li>This may trigger their digital will execution process</li>
              <li>As a trusted contact, your verification may be needed</li>
              <li>This is an automated alert to keep you informed</li>
            </ul>
            
            <h3>Recommended Next Steps:</h3>
            <ul>
              <li><strong>Try to contact ${userName} directly</strong> using your usual communication methods</li>
              <li><strong>If you can reach them,</strong> ask them to log into their Volnt account</li>
              <li><strong>If you cannot reach them,</strong> be prepared for potential verification requests</li>
              <li><strong>Keep this email</strong> for your records</li>
            </ul>
            
            ${customMessage ? `<div class="custom-message"><h3>Additional Message:</h3><p>${customMessage}</p></div>` : ''}
            
            <p><strong>Contact Information:</strong><br>
            Email: <a href="mailto:${userEmail}">${userEmail}</a></p>
            
            <p><em>This is an automated alert. No immediate action is required unless you receive a specific verification request.</em></p>
          </div>
          <div class="footer">
            <p>This is an automated alert from Volnt's trigger system.</p>
            <p><strong>Volnt</strong> - Secure your digital legacy</p>
          </div>
        </div>
      </body>
    </html>
  `
}

function generateVerificationRequestEmail(userName: string, userEmail: string, customMessage?: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Verification Request</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: #ffffff; 
          }
          .header { 
            background: linear-gradient(135deg, #7c3aed, #6d28d9); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
          }
          .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
          }
          .content { 
            padding: 30px 20px; 
            background: #ffffff; 
          }
          .verification-box { 
            background: #ede9fe; 
            border: 1px solid #7c3aed; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
          }
          .verification-box h2 { 
            color: #7c3aed; 
            margin-top: 0; 
          }
          .custom-message { 
            background: #f3f4f6; 
            border-left: 4px solid #6b7280; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0; 
          }
          .button { 
            display: inline-block; 
            background: #7c3aed; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 8px; 
            margin: 20px 0; 
            font-weight: 500; 
          }
          .footer { 
            padding: 20px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            background: #f9fafb; 
          }
          ul { 
            padding-left: 20px; 
          }
          li { 
            margin: 8px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔍 Verification Required</h1>
          </div>
          <div class="content">
            <h2>Hello {RECIPIENT_NAME},</h2>
            
            <div class="verification-box">
              <h2>Verification Request for ${userName}</h2>
              <p>We need your help to verify the status of <strong>${userName}</strong> (${userEmail}).</p>
            </div>
            
            <h3>Why this verification is needed:</h3>
            <ul>
              <li>${userName} has been inactive for an extended period</li>
              <li>Their digital will execution process has been triggered</li>
              <li>We need trusted contacts to verify their status before proceeding</li>
              <li>This helps prevent accidental will execution</li>
            </ul>
            
            <h3>How to respond:</h3>
            <ul>
              <li><strong>Contact ${userName}</strong> using the information below</li>
              <li><strong>Verify their current status</strong> and wellbeing</li>
              <li><strong>Respond to this verification request</strong> with your findings</li>
              <li><strong>Be honest</strong> about whether you can or cannot reach them</li>
            </ul>
            
            <p><strong>Contact Information:</strong><br>
            Email: <a href="mailto:${userEmail}">${userEmail}</a></p>
            
            ${customMessage ? `<div class="custom-message"><h3>Additional Instructions:</h3><p>${customMessage}</p></div>` : ''}
            
            <div style="text-align: center;">
              <a href="mailto:<EMAIL>?subject=Verification Response for ${userName}&body=I have attempted to contact ${userName}. My findings: " class="button">Respond to Verification</a>
            </div>
            
            <p><em><strong>Important:</strong> Please respond within 48 hours. Your response is crucial for the proper execution of ${userName}'s digital will.</em></p>
          </div>
          <div class="footer">
            <p>This verification request was sent by Volnt's automated system.</p>
            <p><strong>Volnt</strong> - Secure your digital legacy</p>
          </div>
        </div>
      </body>
    </html>
  `
}