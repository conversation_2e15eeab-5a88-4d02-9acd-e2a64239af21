import { corsHeaders } from '../_shared/cors.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured')
    }

    const { contactEmail, contactName, userEmail, userName } = await req.json()

    if (!contactEmail || !contactName || !userEmail || !userName) {
      throw new Error('Missing required parameters')
    }

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You've been added as a trusted contact</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: #ffffff; 
            }
            .header { 
              background: linear-gradient(135deg, #059669, #047857); 
              color: white; 
              padding: 30px 20px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 24px; 
              font-weight: 600; 
            }
            .content { 
              padding: 30px 20px; 
              background: #ffffff; 
            }
            .content h2 { 
              color: #1f2937; 
              margin-top: 0; 
            }
            .warning-box { 
              background: #fef3c7; 
              border: 1px solid #f59e0b; 
              padding: 20px; 
              border-radius: 8px; 
              margin: 20px 0; 
            }
            .warning-box h3 { 
              color: #92400e; 
              margin-top: 0; 
            }
            .info-box { 
              background: #f0f9ff; 
              border-left: 4px solid #059669; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 0 8px 8px 0; 
            }
            .button { 
              display: inline-block; 
              background: #059669; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 20px 0; 
              font-weight: 500; 
            }
            .footer { 
              padding: 20px; 
              text-align: center; 
              color: #6b7280; 
              font-size: 14px; 
              background: #f9fafb; 
            }
            ul { 
              padding-left: 20px; 
            }
            li { 
              margin: 8px 0; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🤝 Volnt - Trusted Contact</h1>
            </div>
            <div class="content">
              <h2>Hello ${contactName},</h2>
              
              <div class="info-box">
                <p><strong>${userName}</strong> has added you as a trusted contact for their digital will and testament on Volnt.</p>
              </div>
              
              <div class="warning-box">
                <h3>⚠️ Important Responsibility</h3>
                <p>As a trusted contact, you may be contacted to verify ${userName}'s status if they become inactive for an extended period. This is an important role in protecting their digital legacy.</p>
              </div>
              
              <h3>Your Role as a Trusted Contact:</h3>
              <ul>
                <li><strong>Verification requests:</strong> You may receive emails asking you to verify ${userName}'s status if they become unresponsive</li>
                <li><strong>Status confirmation:</strong> You'll help confirm their wellbeing during the will execution process</li>
                <li><strong>False trigger protection:</strong> Your response helps protect against accidental will execution</li>
                <li><strong>Emergency contact:</strong> You serve as a reliable point of contact for ${userName}</li>
              </ul>
              
              <h3>What to Expect:</h3>
              <ul>
                <li>Periodic check-in emails (only when ${userName} has been inactive)</li>
                <li>Clear verification requests with simple response instructions</li>
                <li>No access to ${userName}'s personal information or assets</li>
                <li>Respectful and secure communication from Volnt</li>
              </ul>
              
              <h3>How to Respond:</h3>
              <p>If you receive a verification request:</p>
              <ul>
                <li>Try to contact ${userName} using your usual communication methods</li>
                <li>Respond to the verification email with your findings</li>
                <li>Be honest about whether you can or cannot reach them</li>
              </ul>
              
              <p>If you have questions about this role, need to update your contact information, or want to be removed as a trusted contact, please reach out to ${userName} directly at <a href="mailto:${userEmail}">${userEmail}</a>.</p>
              
              <div style="text-align: center;">
                <a href="https://volnt.xyz" class="button">Learn More About Volnt</a>
              </div>
            </div>
            <div class="footer">
              <p>This email was sent by Volnt on behalf of ${userName}.</p>
              <p><strong>Volnt</strong> - Secure your digital legacy</p>
              <p>If you believe you received this email in error, please contact ${userName} directly.</p>
            </div>
          </div>
        </body>
      </html>
    `

    const emailData = {
      from: 'Volnt <<EMAIL>>',
      to: [contactEmail],
      subject: `You've been added as a trusted contact by ${userName}`,
      html: emailHtml,
    }

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify(emailData),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Resend API error: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    
    return new Response(JSON.stringify({ 
      success: true, 
      messageId: result.id,
      message: 'Trusted contact notification sent successfully' 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in send-trusted-contact-notification:', error)
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || 'Failed to send trusted contact notification' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})