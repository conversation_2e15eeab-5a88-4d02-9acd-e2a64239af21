import { corsHeaders } from '../_shared/cors.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured')
    }

    const { beneficiaryEmail, beneficiaryName, userEmail, userName } = await req.json()

    if (!beneficiaryEmail || !beneficiaryName || !userEmail || !userName) {
      throw new Error('Missing required parameters')
    }

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You've been added as a beneficiary</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: #ffffff; 
            }
            .header { 
              background: linear-gradient(135deg, #2563eb, #1d4ed8); 
              color: white; 
              padding: 30px 20px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 24px; 
              font-weight: 600; 
            }
            .content { 
              padding: 30px 20px; 
              background: #ffffff; 
            }
            .content h2 { 
              color: #1f2937; 
              margin-top: 0; 
            }
            .highlight-box { 
              background: #f3f4f6; 
              border-left: 4px solid #2563eb; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 0 8px 8px 0; 
            }
            .button { 
              display: inline-block; 
              background: #2563eb; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 20px 0; 
              font-weight: 500; 
            }
            .footer { 
              padding: 20px; 
              text-align: center; 
              color: #6b7280; 
              font-size: 14px; 
              background: #f9fafb; 
            }
            ul { 
              padding-left: 20px; 
            }
            li { 
              margin: 8px 0; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🛡️ Volnt - Digital Will & Testament</h1>
            </div>
            <div class="content">
              <h2>Hello ${beneficiaryName},</h2>
              
              <div class="highlight-box">
                <p><strong>${userName}</strong> has added you as a beneficiary in their digital will and testament on Volnt.</p>
              </div>
              
              <p>This is an important designation that means ${userName} has chosen to include you in their digital legacy planning.</p>
              
              <h3>What this means:</h3>
              <ul>
                <li>You are now listed as a beneficiary in their digital will</li>
                <li>You may receive access to specific digital or physical assets when the will is executed</li>
                <li>You will be notified of any important updates to your beneficiary status</li>
                <li>Your contact information will be kept secure and used only for will-related communications</li>
              </ul>
              
              <h3>Next Steps:</h3>
              <ul>
                <li><strong>Keep this email</strong> for your records</li>
                <li><strong>Ensure your contact information stays current</strong> - reach out to ${userName} if you change your email or phone number</li>
                <li><strong>Consider creating your own digital will</strong> to protect your own digital legacy</li>
              </ul>
              
              <p>If you have any questions about this designation or need to discuss your role as a beneficiary, please contact ${userName} directly at <a href="mailto:${userEmail}">${userEmail}</a>.</p>
              
              <div style="text-align: center;">
                <a href="https://volnt.xyz" class="button">Learn More About Volnt</a>
              </div>
            </div>
            <div class="footer">
              <p>This email was sent by Volnt on behalf of ${userName}.</p>
              <p><strong>Volnt</strong> - Secure your digital legacy</p>
              <p>If you believe you received this email in error, please contact ${userName} directly.</p>
            </div>
          </div>
        </body>
      </html>
    `

    const emailData = {
      from: 'Volnt <<EMAIL>>',
      to: [beneficiaryEmail],
      subject: `You've been added as a beneficiary by ${userName}`,
      html: emailHtml,
    }

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify(emailData),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Resend API error: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    
    return new Response(JSON.stringify({ 
      success: true, 
      messageId: result.id,
      message: 'Beneficiary notification sent successfully' 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in send-beneficiary-notification:', error)
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || 'Failed to send beneficiary notification' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})