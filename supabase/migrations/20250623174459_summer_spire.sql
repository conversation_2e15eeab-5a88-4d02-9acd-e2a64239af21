/*
  # Add Digital Executor functionality to beneficiaries

  1. Changes
    - Add `is_digital_executor` column to beneficiaries table
    - Add check constraint to ensure only one digital executor per user
    - Update existing records to have false as default

  2. Security
    - Maintains existing RLS policies
    - Adds proper constraint for data integrity
*/

-- Add is_digital_executor column with default value
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'beneficiaries' AND column_name = 'is_digital_executor'
  ) THEN
    ALTER TABLE beneficiaries ADD COLUMN is_digital_executor BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Update existing records to have false as default
UPDATE beneficiaries 
SET is_digital_executor = false 
WHERE is_digital_executor IS NULL;

-- Create a function to ensure only one digital executor per user
CREATE OR REPLACE FUNCTION ensure_single_digital_executor()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting a beneficiary as digital executor
  IF NEW.is_digital_executor = true THEN
    -- Set all other beneficiaries for this user to false
    UPDATE beneficiaries 
    SET is_digital_executor = false 
    WHERE user_id = NEW.user_id 
    AND id != NEW.id 
    AND is_digital_executor = true;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce single digital executor constraint
DROP TRIGGER IF EXISTS ensure_single_digital_executor_trigger ON beneficiaries;
CREATE TRIGGER ensure_single_digital_executor_trigger
  BEFORE INSERT OR UPDATE ON beneficiaries
  FOR EACH ROW
  WHEN (NEW.is_digital_executor = true)
  EXECUTE FUNCTION ensure_single_digital_executor();