/*
  # Add source and beneficiary_id columns to trusted_contacts

  1. Changes
    - Add `source` column to track if contact is from beneficiary or custom
    - Add `beneficiary_id` column to link to beneficiaries table
    - Add check constraint for source values
    - Update existing data to have 'custom' source
*/

-- Add source column with default value
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trusted_contacts' AND column_name = 'source'
  ) THEN
    ALTER TABLE trusted_contacts ADD COLUMN source TEXT DEFAULT 'custom';
  END IF;
END $$;

-- Add beneficiary_id column
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trusted_contacts' AND column_name = 'beneficiary_id'
  ) THEN
    ALTER TABLE trusted_contacts ADD COLUMN beneficiary_id UUID REFERENCES beneficiaries(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Update existing records to have 'custom' source
UPDATE trusted_contacts 
SET source = 'custom' 
WHERE source IS NULL;

-- Add check constraint for source values
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'trusted_contacts_source_check' 
    AND table_name = 'trusted_contacts'
  ) THEN
    ALTER TABLE trusted_contacts 
    ADD CONSTRAINT trusted_contacts_source_check 
    CHECK (source IN ('beneficiary', 'custom'));
  END IF;
END $$;