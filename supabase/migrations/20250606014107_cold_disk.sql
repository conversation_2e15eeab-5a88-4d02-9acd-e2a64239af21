/*
  # Digital Will & Testament Database Schema

  1. New Tables
    - `profiles` - User profile information
    - `assets` - Digital and physical assets inventory
    - `asset_details` - Additional encrypted details for assets
    - `beneficiaries` - People who will inherit assets
    - `asset_beneficiaries` - Junction table for assets and beneficiaries
    - `will_documents` - Will document content
    - `trusted_contacts` - Contacts for verification during trigger process
    - `trigger_settings` - Settings for will execution triggers
    - `confirmation_steps` - Steps required before will execution
    - `security_questions` - Security questions for account recovery
    - `audit_logs` - Security audit trail
  
  2. Security
    - Enable RLS on all tables
    - Create policies for secure access
    - Set up encryption for sensitive data
*/

-- Create profiles table to store user information
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  phone_number TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create assets table to track all user assets
CREATE TABLE IF NOT EXISTS assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  value NUMERIC,
  currency TEXT DEFAULT 'USD',
  location TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create asset_details table for encrypted sensitive information
CREATE TABLE IF NOT EXISTS asset_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  key_name TEXT NOT NULL,
  encrypted_value TEXT NOT NULL,
  iv TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(asset_id, key_name)
);

-- Create beneficiaries table
CREATE TABLE IF NOT EXISTS beneficiaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone_number TEXT,
  relationship TEXT,
  access_level TEXT NOT NULL DEFAULT 'LIMITED',
  notification_preference TEXT NOT NULL DEFAULT 'EMAIL',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create junction table for assets and beneficiaries
CREATE TABLE IF NOT EXISTS asset_beneficiaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  beneficiary_id UUID NOT NULL REFERENCES beneficiaries(id) ON DELETE CASCADE,
  percentage INTEGER NOT NULL DEFAULT 100,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(asset_id, beneficiary_id)
);

-- Create will documents table
CREATE TABLE IF NOT EXISTS will_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL DEFAULT 'My Digital Will & Testament',
  content TEXT,
  is_published BOOLEAN DEFAULT false,
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create trusted contacts table
CREATE TABLE IF NOT EXISTS trusted_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone_number TEXT,
  relationship TEXT,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create trigger settings table
CREATE TABLE IF NOT EXISTS trigger_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  inactivity_period INTEGER NOT NULL DEFAULT 90, -- in days
  reminder_frequency INTEGER NOT NULL DEFAULT 30, -- in days
  verification_method TEXT NOT NULL DEFAULT 'EMAIL',
  is_active BOOLEAN DEFAULT true,
  last_activity TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(user_id)
);

-- Create confirmation steps table
CREATE TABLE IF NOT EXISTS confirmation_steps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  trigger_id UUID NOT NULL REFERENCES trigger_settings(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  step_order INTEGER NOT NULL,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create security questions table
CREATE TABLE IF NOT EXISTS security_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  encrypted_answer TEXT NOT NULL,
  iv TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  details TEXT,
  ip_address TEXT,
  user_agent TEXT,
  timestamp TIMESTAMPTZ DEFAULT now()
);

-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE beneficiaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_beneficiaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE will_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE trusted_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE trigger_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE confirmation_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

-- Create policies for assets
CREATE POLICY "Users can view their own assets"
  ON assets FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own assets"
  ON assets FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own assets"
  ON assets FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own assets"
  ON assets FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for asset_details
CREATE POLICY "Users can view their own asset details"
  ON asset_details FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_details.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert their own asset details"
  ON asset_details FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_details.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can update their own asset details"
  ON asset_details FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_details.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete their own asset details"
  ON asset_details FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_details.asset_id
    AND assets.user_id = auth.uid()
  ));

-- Create policies for beneficiaries
CREATE POLICY "Users can view their own beneficiaries"
  ON beneficiaries FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own beneficiaries"
  ON beneficiaries FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own beneficiaries"
  ON beneficiaries FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own beneficiaries"
  ON beneficiaries FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for asset_beneficiaries
CREATE POLICY "Users can view their own asset_beneficiaries"
  ON asset_beneficiaries FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_beneficiaries.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert their own asset_beneficiaries"
  ON asset_beneficiaries FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_beneficiaries.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can update their own asset_beneficiaries"
  ON asset_beneficiaries FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_beneficiaries.asset_id
    AND assets.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete their own asset_beneficiaries"
  ON asset_beneficiaries FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM assets
    WHERE assets.id = asset_beneficiaries.asset_id
    AND assets.user_id = auth.uid()
  ));

-- Create policies for will_documents
CREATE POLICY "Users can view their own will_documents"
  ON will_documents FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own will_documents"
  ON will_documents FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own will_documents"
  ON will_documents FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own will_documents"
  ON will_documents FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for trusted_contacts
CREATE POLICY "Users can view their own trusted_contacts"
  ON trusted_contacts FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trusted_contacts"
  ON trusted_contacts FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trusted_contacts"
  ON trusted_contacts FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trusted_contacts"
  ON trusted_contacts FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for trigger_settings
CREATE POLICY "Users can view their own trigger_settings"
  ON trigger_settings FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trigger_settings"
  ON trigger_settings FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trigger_settings"
  ON trigger_settings FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trigger_settings"
  ON trigger_settings FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for confirmation_steps
CREATE POLICY "Users can view their own confirmation_steps"
  ON confirmation_steps FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM trigger_settings
    WHERE trigger_settings.id = confirmation_steps.trigger_id
    AND trigger_settings.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert their own confirmation_steps"
  ON confirmation_steps FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM trigger_settings
    WHERE trigger_settings.id = confirmation_steps.trigger_id
    AND trigger_settings.user_id = auth.uid()
  ));

CREATE POLICY "Users can update their own confirmation_steps"
  ON confirmation_steps FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM trigger_settings
    WHERE trigger_settings.id = confirmation_steps.trigger_id
    AND trigger_settings.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete their own confirmation_steps"
  ON confirmation_steps FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM trigger_settings
    WHERE trigger_settings.id = confirmation_steps.trigger_id
    AND trigger_settings.user_id = auth.uid()
  ));

-- Create policies for security_questions
CREATE POLICY "Users can view their own security_questions"
  ON security_questions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own security_questions"
  ON security_questions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own security_questions"
  ON security_questions FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own security_questions"
  ON security_questions FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for audit_logs
CREATE POLICY "Users can view their own audit_logs"
  ON audit_logs FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Service role can insert audit_logs"
  ON audit_logs FOR INSERT
  WITH CHECK (true);

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_assets_updated_at
BEFORE UPDATE ON assets
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_asset_details_updated_at
BEFORE UPDATE ON asset_details
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_beneficiaries_updated_at
BEFORE UPDATE ON beneficiaries
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_asset_beneficiaries_updated_at
BEFORE UPDATE ON asset_beneficiaries
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_will_documents_updated_at
BEFORE UPDATE ON will_documents
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_trusted_contacts_updated_at
BEFORE UPDATE ON trusted_contacts
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_trigger_settings_updated_at
BEFORE UPDATE ON trigger_settings
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_confirmation_steps_updated_at
BEFORE UPDATE ON confirmation_steps
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_security_questions_updated_at
BEFORE UPDATE ON security_questions
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Create function to automatically create a user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name'
  );
  
  -- Create default will document
  INSERT INTO public.will_documents (user_id, title)
  VALUES (NEW.id, 'My Digital Will & Testament');
  
  -- Create default trigger settings
  INSERT INTO public.trigger_settings (user_id)
  VALUES (NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();