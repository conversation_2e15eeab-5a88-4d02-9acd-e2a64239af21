/*
  # Add trusted contacts source and beneficiary relationship

  1. Changes
    - Add source column to trusted_contacts table with default 'custom'
    - Add beneficiary_id column for linking to beneficiaries
    - Add check constraint for source values
    - Update existing records to have 'custom' source

  2. Security
    - Maintains existing RLS policies
    - Adds proper foreign key constraint
*/

-- Add source column with default value
ALTER TABLE trusted_contacts 
ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'custom';

-- Add beneficiary_id column
ALTER TABLE trusted_contacts 
ADD COLUMN IF NOT EXISTS beneficiary_id UUID REFERENCES beneficiaries(id) ON DELETE CASCADE;

-- Update existing records to have 'custom' source
UPDATE trusted_contacts 
SET source = 'custom' 
WHERE source IS NULL;

-- Add check constraint for source values (without IF NOT EXISTS)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'trusted_contacts_source_check' 
    AND table_name = 'trusted_contacts'
  ) THEN
    ALTER TABLE trusted_contacts 
    ADD CONSTRAINT trusted_contacts_source_check 
    CHECK (source IN ('beneficiary', 'custom'));
  END IF;
END $$;