<template>
  <footer class="bg-base-200 py-8">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between">
        <div class="mb-6 md:mb-0">
          <div class="flex items-center mb-4">
            <img src="/images/volnt.svg" alt="Volnt" class="h-10 w-10 mr-3" />
            <h2 class="text-xl font-bold">Volnt</h2>
          </div>
          <p class="max-w-md text-base-content/80">
            Secure your digital legacy and ensure your wishes are respected. Our platform helps you create a comprehensive digital will.
          </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
          <div>
            <h3 class="font-semibold mb-3">Product</h3>
            <ul class="space-y-2">
              <li><NuxtLink to="/features" class="hover:text-primary">Features</NuxtLink></li>
              <li><NuxtLink to="/pricing" class="hover:text-primary">Pricing</NuxtLink></li>
              <li><NuxtLink to="/faq" class="hover:text-primary">FAQ</NuxtLink></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold mb-3">Company</h3>
            <ul class="space-y-2">
              <li><NuxtLink to="/about" class="hover:text-primary">About Us</NuxtLink></li>
              <li><NuxtLink to="/blog" class="hover:text-primary">Blog</NuxtLink></li>
              <li><NuxtLink to="/contact" class="hover:text-primary">Contact</NuxtLink></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold mb-3">Legal</h3>
            <ul class="space-y-2">
              <li><NuxtLink to="/terms" class="hover:text-primary">Terms</NuxtLink></li>
              <li><NuxtLink to="/privacy" class="hover:text-primary">Privacy</NuxtLink></li>
              <li><NuxtLink to="/security" class="hover:text-primary">Security</NuxtLink></li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="border-t border-base-300 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-sm text-base-content/60">
          &copy; {{ currentYear }} Volnt. All rights reserved.
        </p>
        
        <div class="flex space-x-4 mt-4 md:mt-0">
          <a href="#" class="btn btn-circle btn-ghost btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-current">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
            </svg>
          </a>
          <a href="#" class="btn btn-circle btn-ghost btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-current">
              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
            </svg>
          </a>
          <a href="#" class="btn btn-circle btn-ghost btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-current">
              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const currentYear = ref(2024);

onMounted(() => {
  currentYear.value = new Date().getFullYear();
});
</script>