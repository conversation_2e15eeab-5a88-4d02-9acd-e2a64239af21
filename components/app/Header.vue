<template>
  <header class="bg-base-100 shadow-sm sticky top-0 z-10">
    <div class="container mx-auto px-4">
      <div class="navbar">
        <div class="navbar-start">
          <NuxtLink to="/" class="btn btn-ghost normal-case text-xl flex items-center">
            <img src="/images/volnt.svg" alt="Volnt" class="h-8 w-8 mr-2" />
            <span class="hidden sm:inline font-bold">Volnt</span>
          </NuxtLink>
        </div>
        <div class="navbar-center hidden lg:flex">
          <ul class="menu menu-horizontal px-1">
            <li v-if="user"><NuxtLink to="/dashboard">Dashboard</NuxtLink></li>
            <li><NuxtLink to="/about">About</NuxtLink></li>
            <li v-if="!user"><NuxtLink to="/auth/login">Login</NuxtLink></li>
          </ul>
        </div>
        <div class="navbar-end">
          <ClientOnly>
            <div v-if="user" class="dropdown dropdown-end ml-2">
              <label tabindex="0" class="btn btn-ghost btn-circle avatar">
                <div class="w-10 rounded-full bg-primary text-white flex items-center justify-center">
                  {{ userInitials }}
                </div>
              </label>
              <ul tabindex="0" class="dropdown-content z-10 menu p-2 shadow bg-base-100 rounded-box w-52">
                <li><NuxtLink to="/dashboard/profile">Profile</NuxtLink></li>
                <li><NuxtLink to="/dashboard/settings">Settings</NuxtLink></li>
                <li><a @click.prevent="handleLogout">Logout</a></li>
              </ul>
            </div>
          </ClientOnly>
          
          <button v-if="!user" class="btn btn-primary ml-2 hidden sm:flex">
            <NuxtLink to="/auth/register" class="text-white">Get Started</NuxtLink>
          </button>
          
          <div class="dropdown dropdown-end lg:hidden ml-2">
            <label tabindex="0" class="btn btn-ghost btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </label>
            <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52 right-0">
              <li v-if="user"><NuxtLink to="/dashboard">Dashboard</NuxtLink></li>
              <li><NuxtLink to="/about">About</NuxtLink></li>
              <li v-if="!user"><NuxtLink to="/auth/login">Login</NuxtLink></li>
              <li v-if="!user"><NuxtLink to="/auth/register">Register</NuxtLink></li>
            </ul>
          </div>
          
          <!-- Theme Switcher -->
          <ThemeSwitcher/>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ThemeSwitcher from '~/components/ui/ThemeSwitcher.vue';

const supabase = useSupabaseClient();
const user = useSupabaseUser();
const router = useRouter();

const userInitials = computed(() => {
  if (!user.value?.email) return '?';
  return user.value.email.substring(0, 2).toUpperCase();
});

const handleLogout = async () => {
  await supabase.auth.signOut();
  router.push('/');
};
</script>