<template>
  <div class="wysiwyg-editor bg-base-100 border border-base-300 rounded-lg">
    <!-- Editor Menu -->
    <div class="flex flex-wrap gap-1 p-2 border-b border-base-300">
      <button
        v-for="item in menuItems"
        :key="item.label"
        @click="item.action"
        class="btn btn-sm btn-ghost tooltip tooltip-bottom"
        :class="{ 'btn-primary text-white': isActive(item) }"
        :data-tip="item.label"
      >
        <Icon :icon="item.icon" class="h-4 w-4" />
      </button>
    </div>
    <InsertAssetModal :show="showAssetModal" @close="showAssetModal = false" @created="insertAsset" />
    <InsertBeneficiaryModal :show="showBeneficiaryModal" @close="showBeneficiaryModal = false" @created="insertBeneficiary" />
    <!-- Editor Content -->
    <editor-content :editor="editor" class="p-4 min-h-[300px] max-h-[600px] overflow-y-auto prose max-w-none" />
  </div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Underline from '@tiptap/extension-underline';
import Table from '@tiptap/extension-table';
import InsertAssetModal from './InsertAssetModal.vue';
import InsertBeneficiaryModal from './InsertBeneficiaryModal.vue';
import { ref, watch, onMounted } from 'vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { Icon } from "@iconify/vue";

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
}>();

const emit = defineEmits(['update:modelValue']);

// Initialize TipTap editor
const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    Placeholder.configure({
      placeholder: props.placeholder || 'Start writing your will here...',
    }),
    Image,
    Link.configure({
      openOnClick: false,
    }),
    TextStyle,
    TaskList,
    TaskItem.configure({
      nested: true,
    }),
    Underline,
    Table.configure({
      resizable: true,
    }),
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML());
  },
});

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newContent) => {
    // Only update if the editor exists and the content is different
    const currentContent = editor.value?.getHTML();
    if (editor.value && newContent !== currentContent) {
      editor.value.commands.setContent(newContent, false);
    }
  }
);

// Check if a menu item is active
const isActive = (item: any) => {
  if (!editor.value) return false;
  
  if (item.type === 'mark') {
    return editor.value.isActive(item.name, item.attributes || {});
  }
  
  if (item.type === 'node') {
    return editor.value.isActive(item.name);
  }
  
  return false;
};

const showAssetModal = ref(false);
const showBeneficiaryModal = ref(false);

function insertAsset(asset: any) {
  if (editor.value) {
    editor.value.chain().focus().insertContent(`[Asset: ${asset.name}]`).run();
  }
}
function insertBeneficiary(beneficiary: any) {
  if (editor.value) {
    editor.value.chain().focus().insertContent(`[Beneficiary: ${beneficiary.name}]`).run();
  }
}

// Editor menu items
const menuItems = [
  {
    type: 'node',
    name: 'heading',
    attributes: { level: 1 },
    icon: 'fluent:text-header-1-20-filled',
    label: 'Heading 1',
    action: () => editor.value?.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    type: 'node',
    name: 'heading',
    attributes: { level: 2 },
    icon: 'fluent:text-header-2-20-filled',
    label: 'Heading 2',
    action: () => editor.value?.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    type: 'mark',
    name: 'bold',
    icon: 'octicon:bold-16',
    label: 'Bold',
    action: () => editor.value?.chain().focus().toggleBold().run(),
  },
  {
    type: 'mark',
    name: 'italic',
    icon: 'mdi:format-italic',
    label: 'Italic',
    action: () => editor.value?.chain().focus().toggleItalic().run(),
  },
  {
    type: 'mark',
    name: 'underline',
    icon: 'mdi:format-underline',
    label: 'Underline',
    action: () => editor.value?.chain().focus().toggleUnderline().run(),
  },
  {
    type: 'node',
    name: 'bulletList',
    icon: 'mdi:format-list-bulleted',
    label: 'Bullet List',
    action: () => editor.value?.chain().focus().toggleBulletList().run(),
  },
  {
    type: 'node',
    name: 'orderedList',
    icon: 'mdi:format-list-numbered',
    label: 'Ordered List',
    action: () => editor.value?.chain().focus().toggleOrderedList().run(),
  },
  {
    type: 'node',
    name: 'taskList',
    icon: 'mdi:format-list-checks',
    label: 'Task List',
    action: () => editor.value?.chain().focus().toggleTaskList().run(),
  },
  {
    type: 'mark',
    name: 'link',
    icon: 'mdi:link',
    label: 'Link',
    action: () => {
      const url = window.prompt('URL');
      if (url) {
        editor.value?.chain().focus().setLink({ href: url }).run();
      } else if (editor.value?.isActive('link')) {
        editor.value?.chain().focus().unsetLink().run();
      }
    },
  },
  {
    type: 'node',
    name: 'image',
    icon: 'mdi:image',
    label: 'Image',
    action: () => {
      const url = window.prompt('Image URL');
      if (url) {
        editor.value?.chain().focus().setImage({ src: url }).run();
      }
    },
  },
  {
    icon: 'mdi:minus',
    label: 'Horizontal Rule',
    action: () => editor.value?.chain().focus().setHorizontalRule().run(),
  },
  {
    icon: 'mdi:format-clear',
    label: 'Clear Formatting',
    action: () => editor.value?.chain().focus().clearNodes().unsetAllMarks().run(),
  },
  {
    icon: 'mdi:treasure-chest',
    label: 'Insert Asset',
    action: () => { showAssetModal.value = true; }
  },
  {
    icon: 'mdi:account-group',
    label: 'Insert Beneficiary',
    action: () => { showBeneficiaryModal.value = true; }
  },
];

const beneficiaryStore = useBeneficiaryStore();

// Fetch beneficiaries on mount
onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries();
});
</script>

<style>
.ProseMirror:focus {
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* TipTap Content Styling */
.ProseMirror h1 {
  font-size: 1.75rem;
  font-weight: bold;
  margin: 1rem 0;
  color: var(--tw-prose-headings);
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
  color: var(--tw-prose-headings);
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 0.75rem 0;
  color: var(--tw-prose-headings);
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.ProseMirror ul[data-type="taskList"] li > label {
  margin-right: 0.5rem;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 3px solid var(--tw-prose-quote-borders);
  padding-left: 1rem;
  margin: 1rem 0;
  color: var(--tw-prose-quotes);
  font-style: italic;
}

.ProseMirror pre {
  background: var(--tw-prose-pre-bg);
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid var(--tw-prose-hr);
  margin: 1.5rem 0;
}

.ProseMirror a {
  color: var(--tw-prose-links);
  text-decoration: underline;
  cursor: pointer;
}
</style>