<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[500px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Edit Beneficiary</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="updateBeneficiary" class="space-y-4">
        <div v-if="errorMessage" class="alert alert-error text-sm">{{ errorMessage }}</div>
        
        <!-- Personal Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Full Name *</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.name"
                class="input input-bordered w-full" 
                placeholder="John Doe"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email Address *</span>
              </label>
              <input 
                type="email" 
                v-model="beneficiaryData.email"
                class="input input-bordered w-full" 
                placeholder="<EMAIL>"
                required
                @input="updateHasChanges"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Phone Number</span>
              </label>
              <input 
                type="tel" 
                v-model="beneficiaryData.phoneNumber"
                class="input input-bordered w-full" 
                placeholder="+****************"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Relationship</span>
              </label>
              <select 
                v-model="beneficiaryData.relationship" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="">Select relationship</option>
                <option value="Spouse">Spouse</option>
                <option value="Child">Child</option>
                <option value="Parent">Parent</option>
                <option value="Sibling">Sibling</option>
                <option value="Friend">Friend</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Access & Permissions -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Access & Permissions</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Access Level</span>
              </label>
              <select 
                v-model="beneficiaryData.accessLevel" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="LIMITED">Limited Access</option>
                <option value="READ_ONLY">Read Only</option>
                <option value="FULL">Full Access</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Notification Preference</span>
              </label>
              <select 
                v-model="beneficiaryData.notificationPreference" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="EMAIL">Email Only</option>
                <option value="SMS">SMS Only</option>
                <option value="BOTH">Email & SMS</option>
                <option value="NONE">No Notifications</option>
              </select>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label cursor-pointer justify-start gap-3">
              <input 
                type="checkbox" 
                v-model="beneficiaryData.isDigitalExecutor"
                class="checkbox checkbox-primary"
                :disabled="currentDigitalExecutor && currentDigitalExecutor.id !== beneficiary?.id"
                @change="updateHasChanges"
              />
              <div>
                <span class="label-text font-medium">Digital Executor</span>
                <div class="text-xs text-base-content/60 mt-1">
                  This person will have authority to manage your digital assets and execute your will
                </div>
                <div v-if="currentDigitalExecutor && currentDigitalExecutor.id !== beneficiary?.id" class="text-xs text-warning mt-1">
                  {{ currentDigitalExecutor.name }} is already set as the digital executor
                </div>
              </div>
            </label>
          </div>
        </div>
        
        <!-- Asset Assignments -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Asset Assignments</h3>
          
          <div v-if="assetStore.assets.length > 0" class="space-y-3 max-h-48 overflow-y-auto">
            <div 
              v-for="asset in assetStore.assets" 
              :key="asset.id"
              class="flex items-center justify-between p-3 bg-base-100 rounded-lg border border-base-300"
            >
              <div class="flex items-center gap-3">
                <input 
                  type="checkbox" 
                  :id="`asset-${asset.id}`"
                  :value="asset.id"
                  v-model="selectedAssets"
                  class="checkbox checkbox-primary"
                  @change="updateHasChanges"
                />
                <label :for="`asset-${asset.id}`" class="cursor-pointer">
                  <div class="font-medium">{{ asset.name }}</div>
                  <div class="text-sm text-base-content/70">{{ getAssetTypeLabel(asset.type) }}</div>
                  <div v-if="asset.value" class="text-xs text-base-content/60">
                    {{ formatCurrency(asset.value, asset.currency || 'USD') }}
                  </div>
                </label>
              </div>
            </div>
          </div>
          
          <div v-else class="text-center py-4">
            <p class="text-base-content/70 text-sm">No assets available</p>
            <p class="text-xs text-base-content/60 mt-1">Create assets first to assign them to beneficiaries</p>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="beneficiaryStore.isLoading || !beneficiaryData.name || !beneficiaryData.email"
            :class="{ 'loading': beneficiaryStore.isLoading }"
          >
            {{ beneficiaryStore.isLoading ? 'Updating...' : 'Update Beneficiary' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useAssetStore } from '~/stores/assetStore';
import type { AccessLevel, NotificationPreference, AssetType, Beneficiary } from '~/types';

const props = defineProps<{ 
  show: boolean,
  beneficiary: Beneficiary | null,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'updated']);

const beneficiaryStore = useBeneficiaryStore();
const assetStore = useAssetStore();
const errorMessage = ref('');

// Form data
const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference,
  isDigitalExecutor: false
});

const selectedAssets = ref<string[]>([]);

const currentDigitalExecutor = computed(() => {
  return beneficiaryStore.digitalExecutor;
});

const getAssetTypeLabel = (type: AssetType) => {
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };

  return typeMap[type] || 'Asset';
};

const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};

const updateHasChanges = () => {
  emit('close', true);
};

const updateBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email || !props.beneficiary) return;
  
  try {
    errorMessage.value = '';
    const updatedBeneficiary = await beneficiaryStore.updateBeneficiary(props.beneficiary.id, {
      name: beneficiaryData.value.name,
      email: beneficiaryData.value.email,
      phoneNumber: beneficiaryData.value.phoneNumber || undefined,
      relationship: beneficiaryData.value.relationship || undefined,
      accessLevel: beneficiaryData.value.accessLevel,
      notificationPreference: beneficiaryData.value.notificationPreference,
      isDigitalExecutor: beneficiaryData.value.isDigitalExecutor
    });
    
    if (updatedBeneficiary) {
      // Handle asset assignments
      const currentAssetIds = props.beneficiary.assets.map(a => a.assetId);
      const newAssetIds = selectedAssets.value;
      
      // Remove unselected assets
      for (const assetId of currentAssetIds) {
        if (!newAssetIds.includes(assetId)) {
          await beneficiaryStore.removeAssetAssignment(props.beneficiary.id, assetId);
        }
      }
      
      // Add newly selected assets
      for (const assetId of newAssetIds) {
        if (!currentAssetIds.includes(assetId)) {
          await beneficiaryStore.assignAsset(props.beneficiary.id, assetId, 100);
        }
      }
      
      // Refresh data to get updated assignments
      await Promise.all([
        assetStore.fetchAssets(),
        beneficiaryStore.fetchBeneficiaries()
      ]);
      
      emit('updated', updatedBeneficiary);
      close();
    }
  } catch (err) {
    console.error('Failed to update beneficiary:', err);
    errorMessage.value = 'Failed to update beneficiary. Please try again.';
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  if (!props.beneficiary) return false;
  
  return beneficiaryData.value.name !== props.beneficiary.name || 
         beneficiaryData.value.email !== props.beneficiary.email || 
         beneficiaryData.value.phoneNumber !== (props.beneficiary.phoneNumber || '') || 
         beneficiaryData.value.relationship !== (props.beneficiary.relationship || '') ||
         beneficiaryData.value.accessLevel !== props.beneficiary.accessLevel ||
         beneficiaryData.value.notificationPreference !== props.beneficiary.notificationPreference ||
         beneficiaryData.value.isDigitalExecutor !== props.beneficiary.isDigitalExecutor ||
         JSON.stringify(selectedAssets.value.sort()) !== JSON.stringify(props.beneficiary.assets.map(a => a.assetId).sort());
}

watch(() => props.show, (val) => {
  if (val && props.beneficiary) {
    // Populate form with beneficiary data
    beneficiaryData.value = {
      name: props.beneficiary.name,
      email: props.beneficiary.email,
      phoneNumber: props.beneficiary.phoneNumber || '',
      relationship: props.beneficiary.relationship || '',
      accessLevel: props.beneficiary.accessLevel,
      notificationPreference: props.beneficiary.notificationPreference,
      isDigitalExecutor: props.beneficiary.isDigitalExecutor
    };
    
    // Set selected assets
    selectedAssets.value = props.beneficiary.assets.map(a => a.assetId);
    errorMessage.value = '';
  }
});

// Fetch current beneficiaries and assets on mount
onMounted(async () => {
  await Promise.all([
    beneficiaryStore.fetchBeneficiaries(),
    assetStore.fetchAssets()
  ]);
});
</script>
