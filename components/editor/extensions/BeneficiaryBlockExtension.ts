import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import BeneficiaryBlockComponent from '../BeneficiaryBlockComponent.vue';

export default Node.create({
   name: 'beneficiaryBlock',
  group: 'block',
  atom: true,
  selectable: true,
  draggable: true,
  
  addAttributes() {
    return {
       beneficiaryId: { default: null },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'beneficiary-block',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['beneficiary-block', mergeAttributes(HTMLAttributes)];
  },
  
  addNodeView() {
    return VueNodeViewRenderer(BeneficiaryBlockComponent);
  },
});