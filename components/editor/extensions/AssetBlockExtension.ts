import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import AssetBlockComponent from '../AssetBlockComponent.vue';

export default Node.create({
  name: 'assetBlock',
  group: 'block',
  atom: true,
  selectable: true,
  draggable: true,
  addAttributes() {
    return {
      assetId: { default: null },
    };
  },
  parseHTML() {
    return [
      {
        tag: 'asset-block',
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ['asset-block', mergeAttributes(HTMLAttributes)];
  },
  addNodeView() {
    return VueNodeViewRenderer(AssetBlockComponent);
  },
});