import { Node, mergeAttributes } from '@tiptap/core';

export default Node.create({
  name: 'instructionBlock',
  
  group: 'block',
  
  content: 'block+',
  
  defining: true,
  
  addAttributes() {
    return {
      type: {
        default: 'general',
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="instruction-block"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'instruction-block' }), 0];
  },
  
  addNodeView() {
    return ({ node }) => {
      const dom = document.createElement('div');
      const contentDOM = document.createElement('div');
      
      const type = node.attrs.type;
      
      dom.classList.add('instruction-block', 'my-4', 'border', 'rounded-lg');
      
      // Style based on instruction type
      switch (type) {
        case 'general':
          dom.classList.add('border-info/30', 'bg-info/5');
          break;
        case 'access':
          dom.classList.add('border-success/30', 'bg-success/5');
          break;
        case 'security':
          dom.classList.add('border-warning/30', 'bg-warning/5');
          break;
        case 'emergency':
          dom.classList.add('border-error/30', 'bg-error/5');
          break;
        default:
          dom.classList.add('border-info/30', 'bg-info/5');
      }
      
      // Header based on type
      const header = document.createElement('div');
      header.classList.add('p-3', 'border-b', 'border-inherit', 'flex', 'items-center', 'gap-2');
      
      // Icon based on type
      let icon = '';
      let title = '';
      
      switch (type) {
        case 'general':
          icon = '<iconify-icon icon="mdi:information" class="h-5 w-5 text-info"></iconify-icon>';
          title = 'General Instructions';
          break;
        case 'access':
          icon = '<iconify-icon icon="mdi:key" class="h-5 w-5 text-success"></iconify-icon>';
          title = 'Access Instructions';
          break;
        case 'security':
          icon = '<iconify-icon icon="mdi:lock" class="h-5 w-5 text-warning"></iconify-icon>';
          title = 'Security Instructions';
          break;
        case 'emergency':
          icon = '<iconify-icon icon="mdi:alert-triangle" class="h-5 w-5 text-error"></iconify-icon>';
          title = 'Emergency Instructions';
          break;
        default:
          icon = '<iconify-icon icon="mdi:information" class="h-5 w-5 text-info"></iconify-icon>';
          title = 'Instructions';
      }
      
      header.innerHTML = `${icon}<span class="font-medium">${title}</span>`;
      dom.appendChild(header);
      
      // Content area
      contentDOM.classList.add('p-3');
      dom.appendChild(contentDOM);
      
      return {
        dom,
        contentDOM,
      };
    };
  },
});
