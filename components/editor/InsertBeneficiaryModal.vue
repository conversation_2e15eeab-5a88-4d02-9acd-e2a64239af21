<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[400px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add Beneficiary</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      <form @submit.prevent="saveBeneficiary" class="space-y-4">
        <div v-if="errorMessage" class="alert alert-error text-sm">{{ errorMessage }}</div>
        <div class="form-control">
          <label class="label"><span class="label-text">Full Name *</span></label>
          <input type="text" v-model="beneficiaryData.name" class="input input-bordered w-full" required />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Email Address *</span></label>
          <input type="email" v-model="beneficiaryData.email" class="input input-bordered w-full" required />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Phone Number</span></label>
          <input type="tel" v-model="beneficiaryData.phoneNumber" class="input input-bordered w-full" />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Relationship</span></label>
          <select v-model="beneficiaryData.relationship" class="select select-bordered w-full">
            <option value="">Select relationship</option>
            <option value="Spouse">Spouse</option>
            <option value="Child">Child</option>
            <option value="Parent">Parent</option>
            <option value="Sibling">Sibling</option>
            <option value="Friend">Friend</option>
            <option value="Other">Other</option>
          </select>
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Access Level</span></label>
          <select v-model="beneficiaryData.accessLevel" class="select select-bordered w-full">
            <option value="LIMITED">Limited - Basic information only</option>
            <option value="READ_ONLY">Read Only - Can view but not modify</option>
            <option value="FULL">Full Access - Can view and manage</option>
          </select>
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Notification Preference</span></label>
          <select v-model="beneficiaryData.notificationPreference" class="select select-bordered w-full">
            <option value="EMAIL">Email notifications</option>
            <option value="SMS">SMS notifications</option>
            <option value="BOTH">Both email and SMS</option>
            <option value="NONE">No notifications</option>
          </select>
        </div>
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button type="submit" class="btn btn-primary" :disabled="beneficiaryStore.isLoading || !beneficiaryData.name || !beneficiaryData.email">
            {{ beneficiaryStore.isLoading ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AccessLevel, NotificationPreference } from '~/types';

const props = defineProps<{ show: boolean }>();
const emit = defineEmits(['close', 'created']);

const beneficiaryStore = useBeneficiaryStore();

const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference
});

const errorMessage = ref('');

const saveBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email) return;
  errorMessage.value = '';
  try {
    const beneficiary = await beneficiaryStore.createBeneficiary({ ...beneficiaryData.value });
    if (beneficiary) {
      emit('created', beneficiary);
      close();
    }
  } catch (err) {
    console.error('createBeneficiary failed', err);
    errorMessage.value = 'Failed to save beneficiary. Please try again.';
  }
};

function close() {
  emit('close');
}
watch(() => props.show, (val) => {
  if (val) {
    beneficiaryData.value = {
      name: '',
      email: '',
      phoneNumber: '',
      relationship: '',
      accessLevel: 'LIMITED' as AccessLevel,
      notificationPreference: 'EMAIL' as NotificationPreference
    };
    errorMessage.value = '';
  }
});
</script> 