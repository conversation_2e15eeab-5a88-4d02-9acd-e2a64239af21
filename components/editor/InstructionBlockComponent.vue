<template>
  <NodeViewWrapper class="instruction-block">
    <div class="card bg-base-100 border border-accent/20 shadow-sm my-4">
      <div class="card-body p-4">
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 rounded-lg bg-accent/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <input 
              v-model="localTitle"
              @blur="updateTitle"
              contenteditable="true"
              class="input input-ghost input-sm font-semibold text-lg p-0 border-none focus:outline-none"
              placeholder="Instruction Title"
            />
          </div>
          
          <div class="dropdown dropdown-end">
            <label tabindex="0" class="btn btn-ghost btn-sm btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" />
              </svg>
            </label>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li><a @click="changeType('general')">General Instructions</a></li>
              <li><a @click="changeType('access')">Access Instructions</a></li>
              <li><a @click="changeType('security')">Security Instructions</a></li>
              <li><a @click="changeType('emergency')">Emergency Instructions</a></li>
              <li class="divider"></li>
              <li><a @click="deleteBlock" class="text-error">Remove Block</a></li>
            </ul>
          </div>
        </div>
        
        <div class="mb-3">
          <span class="badge badge-accent badge-sm">{{ formatInstructionType(props.node.attrs.instructionType) }}</span>
        </div>
        
        <textarea 
          v-model="localContent"
          @blur="updateContent"
          contenteditable="true"
          class="textarea textarea-ghost w-full min-h-[100px] p-0 border-none focus:outline-none resize-none"
          :placeholder="getPlaceholder()"
        ></textarea>
        
        <div v-if="props.node.attrs.instructionType === 'access'" class="mt-3 p-3 bg-warning/10 rounded-lg">
          <div class="flex items-start space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-warning mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div class="text-sm">
              <p class="font-medium text-warning">Security Notice</p>
              <p class="text-base-content/70">Ensure sensitive access information is stored securely and not directly in this document.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper } from '@tiptap/vue-3';
import { ref, watch } from 'vue';

const props = defineProps<{
  node: {
    attrs: {
      instructionType: string;
      title: string;
      content: string;
    };
  };
  updateAttributes: (attributes: Record<string, any>) => void;
  deleteNode: () => void;
}>();

const localTitle = ref(props.node.attrs.title || 'Instructions');
const localContent = ref(props.node.attrs.content || '');

// Watch for external changes
watch(() => props.node.attrs.title, (newTitle) => {
  localTitle.value = newTitle;
});

watch(() => props.node.attrs.content, (newContent) => {
  localContent.value = newContent;
});

const updateTitle = () => {
  props.updateAttributes({ title: localTitle.value });
};

const updateContent = () => {
  props.updateAttributes({ content: localContent.value });
};

const changeType = (type: string) => {
  props.updateAttributes({ instructionType: type });
};

const formatInstructionType = (type: string) => {
  const typeMap: Record<string, string> = {
    general: 'General Instructions',
    access: 'Access Instructions',
    security: 'Security Instructions',
    emergency: 'Emergency Instructions'
  };
  
  return typeMap[type] || 'Instructions';
};

const getPlaceholder = () => {
  const placeholders: Record<string, string> = {
    general: 'Add general instructions for your beneficiaries...',
    access: 'Provide detailed access instructions for accounts, passwords, or security keys...',
    security: 'Include security protocols, verification steps, or safety measures...',
    emergency: 'Specify emergency procedures or contacts in case of urgent situations...'
  };
  
  return placeholders[props.node.attrs.instructionType] || 'Add your instructions here...';
};

const deleteBlock = () => {
  props.deleteNode();
};
</script>

<style scoped>
.instruction-block {
  margin: 1rem 0;
}

.textarea:focus {
  box-shadow: none;
}

.input:focus {
  box-shadow: none;
}
</style>