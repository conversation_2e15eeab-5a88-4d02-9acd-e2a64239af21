<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[600px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add New Asset</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="saveAsset" class="space-y-4">
        <!-- Basic Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Basic Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Name *</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.name"
                class="input input-bordered w-full" 
                placeholder="e.g., Bitcoin Wallet, Gmail Account"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Type *</span>
              </label>
              <select 
                v-model="assetData.type" 
                class="select select-bordered w-full" 
                required
                @change="updateHasChanges"
              >
                <option value="">Select asset type</option>
                <option v-for="type in assetTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="assetData.description"
              class="textarea textarea-bordered w-full" 
              placeholder="Brief description of this asset"
              rows="3"
              @input="updateHasChanges"
            ></textarea>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Estimated Value</span>
              </label>
              <input 
                type="number" 
                v-model.number="assetData.value"
                class="input input-bordered w-full" 
                placeholder="0"
                min="0"
                step="0.01"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Currency</span>
              </label>
              <select 
                v-model="assetData.currency" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Location</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.location"
                class="input input-bordered w-full" 
                placeholder="Physical or digital location"
                @input="updateHasChanges"
              />
            </div>
          </div>
        </div>
        
        <!-- Assign Beneficiaries Section -->
        <div class="card bg-secondary/5 border border-secondary/20 p-4">
          <div class="flex items-center mb-3">
            <Icon icon="mdi:account-group" class="h-5 w-5 text-secondary mr-2" />
            <h3 class="font-semibold text-lg text-secondary">Assign Beneficiaries</h3>
          </div>
          
          <div v-if="beneficiaryStore.beneficiaries.length > 0">
            <p class="text-sm text-base-content/70 mb-3">
              Select beneficiaries who will inherit this asset. You can adjust percentages later.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <label 
                v-for="beneficiary in beneficiaryStore.beneficiaries" 
                :key="beneficiary.id"
                class="label cursor-pointer justify-start p-2 hover:bg-base-200 rounded"
              >
                <input 
                  type="checkbox" 
                  :value="beneficiary.id"
                  v-model="selectedBeneficiaries"
                  class="checkbox checkbox-secondary checkbox-sm mr-3" 
                  @change="updateHasChanges"
                />
                <div class="flex items-center">
                  <div class="avatar mr-2">
                    <div class="w-6 h-6 rounded-full bg-secondary text-white flex items-center justify-center text-xs font-medium"
                         :class="{ 'bg-amber-500': beneficiary.isDigitalExecutor }">
                      {{ getInitials(beneficiary.name) }}
                    </div>
                  </div>
                  <div>
                    <span class="label-text font-medium">{{ beneficiary.name }}</span>
                    <div v-if="beneficiary.isDigitalExecutor" class="badge badge-warning badge-xs ml-1">
                      <Icon icon="mdi:crown" class="h-2 w-2 mr-1" />
                      Executor
                    </div>
                    <div class="text-xs text-base-content/60">{{ beneficiary.relationship || 'Beneficiary' }}</div>
                  </div>
                </div>
              </label>
            </div>
            
            <div v-if="selectedBeneficiaries.length > 0" class="mt-3 p-2 bg-info/10 rounded">
              <div class="text-xs text-info">
                <Icon icon="mdi:information" class="h-3 w-3 inline mr-1" />
                Each selected beneficiary will be assigned 100% of this asset. You can adjust percentages on the asset management page.
              </div>
            </div>
          </div>
          
          <div v-else class="text-center py-4">
            <p class="text-base-content/70 text-sm">No beneficiaries available</p>
            <p class="text-xs text-base-content/60 mt-1">Create beneficiaries first to assign them to assets</p>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="assetStore.isLoading || !assetData.name || !assetData.type"
            :class="{ 'loading': assetStore.isLoading }"
          >
            {{ assetStore.isLoading ? 'Saving...' : 'Save Asset' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AssetType } from '~/types';

const props = defineProps<{ 
  show: boolean,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'created', 'changed']);

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const assetData = ref({
  name: '',
  type: '' as AssetType | '',
  description: '',
  value: undefined as number | undefined,
  currency: 'USD',
  location: ''
});

const selectedBeneficiaries = ref<string[]>([]);

const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const updateHasChanges = () => {
  emit('changed', true);
};

const saveAsset = async () => {
  if (!assetData.value.name || !assetData.value.type) return;
  
  try {
    const asset = await assetStore.createAsset({
      ...assetData.value,
      type: assetData.value.type as AssetType
    });
    
    if (asset) {
      // Assign selected beneficiaries to the new asset
      if (selectedBeneficiaries.value.length > 0) {
        for (const beneficiaryId of selectedBeneficiaries.value) {
          await beneficiaryStore.assignAsset(beneficiaryId, asset.id, 100);
        }
        
        // Refresh data to get updated assignments
        await Promise.all([
          assetStore.fetchAssets(),
          beneficiaryStore.fetchBeneficiaries()
        ]);
      }
      
      emit('created', asset);
      close();
    }
  } catch (err) {
    console.error('Failed to create asset:', err);
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  return assetData.value.name !== '' || 
         assetData.value.type !== '' || 
         assetData.value.description !== '' || 
         assetData.value.value !== undefined || 
         assetData.value.location !== '' ||
         selectedBeneficiaries.value.length > 0;
}

watch(() => props.show, (val) => {
  if (val) {
    assetData.value = {
      name: '',
      type: '',
      description: '',
      value: undefined,
      currency: 'USD',
      location: ''
    };
    selectedBeneficiaries.value = [];
  }
});

// Fetch beneficiaries when modal opens
onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries();
});
</script>