<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[600px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Edit Asset</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="updateAsset" class="space-y-4">
        <!-- Basic Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Basic Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Name *</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.name"
                class="input input-bordered w-full" 
                placeholder="e.g., Bitcoin Wallet, Gmail Account"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Type *</span>
              </label>
              <select 
                v-model="assetData.type" 
                class="select select-bordered w-full" 
                required
                @change="updateHasChanges"
              >
                <option value="">Select asset type</option>
                <option v-for="type in assetTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="assetData.description"
              class="textarea textarea-bordered w-full" 
              placeholder="Additional details about this asset..."
              rows="3"
              @input="updateHasChanges"
            ></textarea>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Estimated Value</span>
              </label>
              <input 
                type="number" 
                v-model.number="assetData.value"
                class="input input-bordered w-full" 
                placeholder="0"
                min="0"
                step="0.01"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Currency</span>
              </label>
              <select 
                v-model="assetData.currency" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Location</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.location"
                class="input input-bordered w-full" 
                placeholder="Physical or digital location"
                @input="updateHasChanges"
              />
            </div>
          </div>
        </div>
        
        <!-- Beneficiaries Assignment -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Assigned Beneficiaries</h3>
          
          <div v-if="beneficiaryStore.beneficiaries.length > 0" class="space-y-3">
            <div 
              v-for="beneficiary in beneficiaryStore.beneficiaries" 
              :key="beneficiary.id"
              class="flex items-center justify-between p-3 bg-base-100 rounded-lg border border-base-300"
            >
              <div class="flex items-center gap-3">
                <input 
                  type="checkbox" 
                  :id="`beneficiary-${beneficiary.id}`"
                  :value="beneficiary.id"
                  v-model="selectedBeneficiaries"
                  class="checkbox checkbox-primary"
                  @change="updateHasChanges"
                />
                <label :for="`beneficiary-${beneficiary.id}`" class="cursor-pointer">
                  <div class="font-medium">{{ beneficiary.name }}</div>
                  <div class="text-sm text-base-content/70">{{ beneficiary.email }}</div>
                  <div v-if="beneficiary.relationship" class="text-xs text-base-content/60">
                    {{ beneficiary.relationship }}
                  </div>
                </label>
              </div>
              
              <div v-if="beneficiary.isDigitalExecutor" class="badge badge-primary badge-sm">
                Digital Executor
              </div>
            </div>
          </div>
          
          <div v-else class="text-center py-4">
            <p class="text-base-content/70 text-sm">No beneficiaries available</p>
            <p class="text-xs text-base-content/60 mt-1">Create beneficiaries first to assign them to assets</p>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="assetStore.isLoading || !assetData.name || !assetData.type"
            :class="{ 'loading': assetStore.isLoading }"
          >
            {{ assetStore.isLoading ? 'Updating...' : 'Update Asset' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AssetType, Asset } from '~/types';

const props = defineProps<{ 
  show: boolean,
  asset: Asset | null,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'updated', 'changed']);

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const assetData = ref({
  name: '',
  type: '' as AssetType | '',
  description: '',
  value: undefined as number | undefined,
  currency: 'USD',
  location: ''
});

const selectedBeneficiaries = ref<string[]>([]);

const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

const updateHasChanges = () => {
  emit('changed', true);
};

const updateAsset = async () => {
  if (!assetData.value.name || !assetData.value.type || !props.asset) return;
  
  try {
    const updatedAsset = await assetStore.updateAsset(props.asset.id, {
      ...assetData.value,
      type: assetData.value.type as AssetType
    });
    
    if (updatedAsset) {
      // Handle beneficiary assignments
      const currentBeneficiaryIds = props.asset.beneficiaries.map(b => b.id);
      const newBeneficiaryIds = selectedBeneficiaries.value;
      
      // Remove unselected beneficiaries
      for (const beneficiaryId of currentBeneficiaryIds) {
        if (!newBeneficiaryIds.includes(beneficiaryId)) {
          await beneficiaryStore.removeAssetAssignment(beneficiaryId, props.asset.id);
        }
      }
      
      // Add newly selected beneficiaries
      for (const beneficiaryId of newBeneficiaryIds) {
        if (!currentBeneficiaryIds.includes(beneficiaryId)) {
          await beneficiaryStore.assignAsset(beneficiaryId, props.asset.id, 100);
        }
      }
      
      // Refresh data to get updated assignments
      await Promise.all([
        assetStore.fetchAssets(),
        beneficiaryStore.fetchBeneficiaries()
      ]);
      
      emit('updated', updatedAsset);
      close();
    }
  } catch (err) {
    console.error('Failed to update asset:', err);
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  if (!props.asset) return false;
  
  return assetData.value.name !== props.asset.name || 
         assetData.value.type !== props.asset.type || 
         assetData.value.description !== (props.asset.description || '') || 
         assetData.value.value !== props.asset.value || 
         assetData.value.currency !== (props.asset.currency || 'USD') ||
         assetData.value.location !== (props.asset.location || '') ||
         JSON.stringify(selectedBeneficiaries.value.sort()) !== JSON.stringify(props.asset.beneficiaries.map(b => b.id).sort());
}

watch(() => props.show, (val) => {
  if (val && props.asset) {
    // Populate form with asset data
    assetData.value = {
      name: props.asset.name,
      type: props.asset.type,
      description: props.asset.description || '',
      value: props.asset.value,
      currency: props.asset.currency || 'USD',
      location: props.asset.location || ''
    };
    
    // Set selected beneficiaries
    selectedBeneficiaries.value = props.asset.beneficiaries.map(b => b.id);
  }
});

// Fetch beneficiaries when modal opens
onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries();
});
</script>
