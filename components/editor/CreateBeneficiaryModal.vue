<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[500px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add New Beneficiary</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="saveBeneficiary" class="space-y-4">
        <div v-if="errorMessage" class="alert alert-error text-sm">{{ errorMessage }}</div>
        
        <!-- Personal Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Full Name *</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.name"
                class="input input-bordered w-full" 
                placeholder="John Doe"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email Address *</span>
              </label>
              <input 
                type="email" 
                v-model="beneficiaryData.email"
                class="input input-bordered w-full" 
                placeholder="<EMAIL>"
                required
                @input="updateHasChanges"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Phone Number</span>
              </label>
              <input 
                type="tel" 
                v-model="beneficiaryData.phoneNumber"
                class="input input-bordered w-full" 
                placeholder="+****************"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Relationship</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.relationship"
                class="input input-bordered w-full" 
                placeholder="e.g., Spouse, Child, Friend"
                @input="updateHasChanges"
              />
            </div>
          </div>
        </div>
        
        <!-- Digital Executor Section -->
        <div class="card bg-amber-50 border border-amber-200 p-4">
          <div class="flex items-center mb-3">
            <Icon icon="mdi:shield-crown" class="h-5 w-5 text-amber-600 mr-2" />
            <h3 class="font-semibold text-base text-amber-800">Digital Executor</h3>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="beneficiaryData.isDigitalExecutor" 
                  class="checkbox checkbox-warning mr-3"
                  @change="updateHasChanges"
                />
                <div>
                  <span class="label-text font-medium">Designate as Digital Executor</span>
                  <div class="text-xs text-base-content/70">
                    This person will manage your digital assets
                  </div>
                </div>
              </div>
            </label>
          </div>
          
          <div v-if="currentDigitalExecutor && beneficiaryData.isDigitalExecutor" class="alert alert-info mt-3">
            <Icon icon="mdi:information" class="h-4 w-4" />
            <div class="text-xs">
              <strong>{{ currentDigitalExecutor.name }}</strong> is currently your Digital Executor. 
              This will transfer that role to the new beneficiary.
            </div>
          </div>
        </div>
        
        <!-- Access & Permissions -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-base mb-3">Access & Permissions</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Access Level</span>
              </label>
              <select 
                v-model="beneficiaryData.accessLevel" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="LIMITED">Limited - Basic information only</option>
                <option value="READ_ONLY">Read Only - Can view but not modify</option>
                <option value="FULL">Full Access - Can view and manage</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Notification Preference</span>
              </label>
              <select 
                v-model="beneficiaryData.notificationPreference" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="EMAIL">Email notifications</option>
                <option value="SMS">SMS notifications</option>
                <option value="BOTH">Both email and SMS</option>
                <option value="NONE">No notifications</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Assign Assets Section -->
        <div class="card bg-primary/5 border border-primary/20 p-4">
          <div class="flex items-center mb-3">
            <Icon icon="mdi:treasure-chest" class="h-5 w-5 text-primary mr-2" />
            <h3 class="font-semibold text-base text-primary">Assign Assets</h3>
          </div>
          
          <div v-if="assetStore.assets.length > 0">
            <p class="text-sm text-base-content/70 mb-3">
              Select assets this beneficiary will inherit. You can adjust percentages later.
            </p>
            
            <div class="grid grid-cols-1 gap-2">
              <label 
                v-for="asset in assetStore.assets" 
                :key="asset.id"
                class="label cursor-pointer justify-start p-2 hover:bg-base-200 rounded"
              >
                <input 
                  type="checkbox" 
                  :value="asset.id"
                  v-model="selectedAssets"
                  class="checkbox checkbox-primary checkbox-sm mr-3" 
                  @change="updateHasChanges"
                />
                <div class="flex items-center">
                  <div class="avatar mr-2">
                    <div class="w-6 h-6 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Icon :icon="getAssetIcon(asset.type)" class="w-3 h-3 text-primary" />
                    </div>
                  </div>
                  <div>
                    <span class="label-text font-medium">{{ asset.name }}</span>
                    <div class="text-xs text-base-content/60">{{ getAssetTypeLabel(asset.type) }}</div>
                    <div v-if="asset.value" class="badge badge-primary badge-xs mt-1">
                      {{ formatCurrency(asset.value, asset.currency || 'USD') }}
                    </div>
                  </div>
                </div>
              </label>
            </div>
            
            <div v-if="selectedAssets.length > 0" class="mt-3 p-2 bg-info/10 rounded">
              <div class="text-xs text-info">
                <Icon icon="mdi:information" class="h-3 w-3 inline mr-1" />
                This beneficiary will be assigned 100% of each selected asset. You can adjust percentages on the beneficiary management page.
              </div>
            </div>
          </div>
          
          <div v-else class="text-center py-4">
            <p class="text-base-content/70 text-sm">No assets available</p>
            <p class="text-xs text-base-content/60 mt-1">Create assets first to assign them to beneficiaries</p>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="beneficiaryStore.isLoading || !beneficiaryData.name || !beneficiaryData.email"
            :class="{ 'loading': beneficiaryStore.isLoading }"
          >
            {{ beneficiaryStore.isLoading ? 'Saving...' : 'Save Beneficiary' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useAssetStore } from '~/stores/assetStore';
import type { AccessLevel, NotificationPreference, AssetType } from '~/types';

const props = defineProps<{ 
  show: boolean,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'created']);

const beneficiaryStore = useBeneficiaryStore();
const assetStore = useAssetStore();
const errorMessage = ref('');

// Form data
const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference,
  isDigitalExecutor: false
});

const selectedAssets = ref<string[]>([]);

const currentDigitalExecutor = computed(() => {
  return beneficiaryStore.digitalExecutor;
});

const getAssetIcon = (type: AssetType) => {
  const iconMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'mdi:monitor',
    CRYPTOCURRENCY: 'mdi:bitcoin',
    SOCIAL_MEDIA: 'mdi:account-group',
    FINANCIAL_ACCOUNT: 'mdi:credit-card',
    REAL_ESTATE: 'mdi:home',
    VEHICLE: 'mdi:car',
    COLLECTIBLE: 'mdi:treasure-chest',
    INSURANCE: 'mdi:shield-check',
    INTELLECTUAL_PROPERTY: 'mdi:lightbulb',
    PERSONAL_BELONGING: 'mdi:bag-personal',
    CUSTOM: 'mdi:tag'
  };

  return iconMap[type] || iconMap.CUSTOM;
};

const getAssetTypeLabel = (type: AssetType) => {
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };

  return typeMap[type] || 'Asset';
};

const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};

const updateHasChanges = () => {
  emit('close', true);
};

const saveBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email) return;
  
  try {
    errorMessage.value = '';
    const beneficiary = await beneficiaryStore.createBeneficiary({
      name: beneficiaryData.value.name,
      email: beneficiaryData.value.email,
      phoneNumber: beneficiaryData.value.phoneNumber || undefined,
      relationship: beneficiaryData.value.relationship || undefined,
      accessLevel: beneficiaryData.value.accessLevel,
      notificationPreference: beneficiaryData.value.notificationPreference,
      isDigitalExecutor: beneficiaryData.value.isDigitalExecutor
    });
    
    if (beneficiary) {
      // Assign selected assets to the new beneficiary
      if (selectedAssets.value.length > 0) {
        for (const assetId of selectedAssets.value) {
          await beneficiaryStore.assignAsset(beneficiary.id, assetId, 100);
        }
        
        // Refresh data to get updated assignments
        await Promise.all([
          assetStore.fetchAssets(),
          beneficiaryStore.fetchBeneficiaries()
        ]);
      }
      
      emit('created', beneficiary);
      close();
    }
  } catch (err) {
    console.error('Failed to create beneficiary:', err);
    errorMessage.value = 'Failed to create beneficiary. Please try again.';
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  return beneficiaryData.value.name !== '' || 
         beneficiaryData.value.email !== '' || 
         beneficiaryData.value.phoneNumber !== '' || 
         beneficiaryData.value.relationship !== '' ||
         beneficiaryData.value.isDigitalExecutor !== false ||
         selectedAssets.value.length > 0;
}

watch(() => props.show, (val) => {
  if (val) {
    beneficiaryData.value = {
      name: '',
      email: '',
      phoneNumber: '',
      relationship: '',
      accessLevel: 'LIMITED' as AccessLevel,
      notificationPreference: 'EMAIL' as NotificationPreference,
      isDigitalExecutor: false
    };
    selectedAssets.value = [];
    errorMessage.value = '';
  }
});

// Fetch current beneficiaries and assets on mount to check for existing digital executor
onMounted(async () => {
  await Promise.all([
    beneficiaryStore.fetchBeneficiaries(),
    assetStore.fetchAssets()
  ]);
});
</script>