<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[400px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add Asset</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      <form @submit.prevent="saveAsset" class="space-y-4">
        <div class="form-control">
          <label class="label"><span class="label-text">Asset Name *</span></label>
          <input type="text" v-model="assetData.name" class="input input-bordered w-full" required />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Asset Type *</span></label>
          <select v-model="assetData.type" class="select select-bordered w-full" required>
            <option value="">Select asset type</option>
            <option v-for="type in assetTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
          </select>
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Description</span></label>
          <textarea v-model="assetData.description" class="textarea textarea-bordered w-full" rows="2"></textarea>
        </div>
        <div class="flex gap-2">
          <input type="number" v-model.number="assetData.value" class="input input-bordered w-1/2" placeholder="Value" min="0" step="0.01" />
          <select v-model="assetData.currency" class="select select-bordered w-1/2">
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="BTC">BTC</option>
          </select>
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Location</span></label>
          <input type="text" v-model="assetData.location" class="input input-bordered w-full" />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Assign Beneficiaries</span></label>
          <select v-model="assetData.beneficiaries" multiple class="select select-bordered w-full">
            <option v-for="ben in beneficiaryStore.beneficiaries" :key="ben.id" :value="ben.id">
              {{ ben.name }}
            </option>
          </select>
        </div>
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button type="submit" class="btn btn-primary" :disabled="assetStore.isLoading || !assetData.name || !assetData.type">
            {{ assetStore.isLoading ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AssetType } from '~/types';

const props = defineProps<{ show: boolean }>();
const emit = defineEmits(['close', 'created']);

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const assetData = ref({
  name: '',
  type: '' as AssetType | '',
  description: '',
  value: undefined as number | undefined,
  currency: 'USD',
  location: '',
  beneficiaries: [] as string[]
});

const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries?.();
});

const saveAsset = async () => {
   if (!assetData.value.name || !assetData.value.type) return;
  try {
    // Pass beneficiary IDs; backend/store should map to full objects or handle join table
    const asset = await assetStore.createAsset({
      ...assetData.value,
      type: assetData.value.type as AssetType,
      beneficiaries: assetData.value.beneficiaries as any // Cast to any to satisfy type checker
    });
    if (asset) {
      emit('created', asset);
      close();
    }
  } catch (err) {
    console.error('createAsset failed', err);
    // TODO: show notification / rollback UI
  }
 };

function close() {
  emit('close');
}
watch(() => props.show, (val) => {
  if (val) {
    assetData.value = {
      name: '',
      type: '',
      description: '',
      value: undefined,
      currency: 'USD',
      location: '',
      beneficiaries: []
    };
  }
});
</script> 