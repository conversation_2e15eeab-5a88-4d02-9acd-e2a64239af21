<template>
  <NodeViewWrapper class="asset-block">
    <div class="card bg-base-100 border border-primary/20 shadow-sm my-4">
      <div class="card-body p-4">
        <div class="flex items-start justify-between">
          <div class="flex items-center space-x-3">
            <div class="avatar">
              <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <Icon :icon="assetIcon" class="w-5 h-5 text-primary" />
              </div>
            </div>
            <div>
              <h4 class="font-semibold text-lg">{{ asset?.name || 'Asset' }}</h4>
              <p class="text-sm text-base-content/70">{{ assetTypeLabel }}</p>
              <div v-if="asset?.value" class="badge badge-primary badge-sm mt-1">
                {{ formatCurrency(asset.value, asset.currency || 'USD') }}
              </div>
            </div>
          </div>
          
          <div class="dropdown dropdown-end">
            <label tabindex="1" class="btn btn-ghost btn-sm btn-circle">
              <Icon icon="mdi:dots-vertical" class="w-4 h-4" />
            </label>
            <ul tabindex="1" class="dropdown-content z-[99] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li><a @click="editAsset">Edit Asset</a></li>
              <li><a @click="viewDetails">View Details</a></li>
              <li><a @click="manageBeneficiaries">Manage Beneficiaries</a></li>
              <li><a @click="deleteBlock" class="text-error">Remove from Will</a></li>
            </ul>
          </div>
        </div>
        
        <div v-if="asset?.description" class="mt-3">
          <p class="text-sm text-base-content/80">{{ asset.description }}</p>
        </div>
        
        <div v-if="asset?.beneficiaries?.length" class="mt-3">
          <div class="text-xs font-medium text-base-content/70 mb-2">Beneficiaries:</div>
          <div class="flex flex-wrap gap-1">
            <div 
              v-for="beneficiary in asset.beneficiaries.slice(0, 3)" 
              :key="beneficiary.id"
              class="badge badge-outline badge-sm"
              :class="{ 'badge-warning': isDigitalExecutor(beneficiary.id) }"
            >
              {{ getBeneficiaryName(beneficiary.id) }} 
              <span v-if="isDigitalExecutor(beneficiary.id)" class="ml-1">
                <Icon icon="mdi:crown" class="h-2 w-2 inline" />
              </span>
              ({{ beneficiary.percentage }}%)
            </div>
            <div v-if="asset.beneficiaries.length > 3" class="badge badge-outline badge-sm">
              +{{ asset.beneficiaries.length - 3 }} more
            </div>
          </div>
        </div>
        
        <div v-if="asset?.location" class="mt-2">
          <div class="text-xs text-base-content/60">
            <Icon icon="mdi:map-marker" class="h-3 w-3 inline mr-1" />
            {{ asset.location }}
          </div>
        </div>
      </div>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper } from '@tiptap/vue-3';
import { computed } from 'vue';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { Asset, AssetType } from '~/types';

const props = defineProps<{
  node: {
    attrs: {
      assetId: string;
    };
  };
  updateAttributes: (attributes: Record<string, any>) => void;
  deleteNode: () => void;
}>();

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const asset = computed(() => {
  return assetStore.assets.find(a => a.id === props.node.attrs.assetId);
});

const assetTypeLabel = computed(() => {
  if (!asset.value) return 'Unknown Asset';
  
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };
  
  return typeMap[asset.value.type] || 'Asset';
});

const assetIcon = computed(() => {
  if (!asset.value) return 'mdi:tag';

  const iconMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'mdi:monitor',
    CRYPTOCURRENCY: 'mdi:bitcoin',
    SOCIAL_MEDIA: 'mdi:account-group',
    FINANCIAL_ACCOUNT: 'mdi:credit-card',
    REAL_ESTATE: 'mdi:home',
    VEHICLE: 'mdi:car',
    COLLECTIBLE: 'mdi:treasure-chest',
    INSURANCE: 'mdi:shield-check',
    INTELLECTUAL_PROPERTY: 'mdi:lightbulb',
    PERSONAL_BELONGING: 'mdi:bag-personal',
    CUSTOM: 'mdi:tag'
  };

  return iconMap[asset.value.type] || iconMap.CUSTOM;
});

const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};

const getBeneficiaryName = (beneficiaryId: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId);
  return beneficiary?.name || 'Unknown';
};

const isDigitalExecutor = (beneficiaryId: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId);
  return beneficiary?.isDigitalExecutor || false;
};

const editAsset = () => {
  navigateTo(`/dashboard/assets/edit/${props.node.attrs.assetId}`);
};

const viewDetails = () => {
  navigateTo(`/dashboard/assets/${props.node.attrs.assetId}`);
};

const manageBeneficiaries = () => {
  navigateTo(`/dashboard/assets/${props.node.attrs.assetId}/beneficiaries`);
};

const deleteBlock = () => {
  props.deleteNode();
};
</script>

<style scoped>
.asset-block {
  margin: 1rem 0;
}
</style>