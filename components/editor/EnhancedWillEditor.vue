<template>
  <div class="enhanced-will-editor">
    <!-- Editor Toolbar -->
    <div class="bg-base-100 border border-base-300 rounded-t-lg p-3">
      <div class="flex flex-wrap gap-2 items-center">
        <!-- Text Formatting -->
        <div class="flex gap-1">
          <button
            @click="editor?.chain().focus().toggleBold().run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('bold') }"
          >
            <Icon icon="mdi:format-bold" class="h-4 w-4" />
          </button>
          
          <button
            @click="editor?.chain().focus().toggleItalic().run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('italic') }"
          >
            <Icon icon="mdi:format-italic" class="h-4 w-4" />
          </button>
          
          <button
            @click="editor?.chain().focus().toggleUnderline().run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('underline') }"
          >
            <Icon icon="mdi:format-underline" class="h-4 w-4" />
          </button>
        </div>
        
        <div class="divider divider-horizontal"></div>
        
        <!-- Headings -->
        <div class="flex gap-1">
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('heading', { level: 1 }) }"
          >
            title
          </button>
          
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('heading', { level: 2 }) }"
          >
            subtitle
          </button>
          
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('heading', { level: 3 }) }"
          >
            body
          </button>
        </div>
        
        <div class="divider divider-horizontal"></div>
        
        <!-- Lists -->
        <div class="flex gap-1">
          <button
            @click="editor?.chain().focus().toggleBulletList().run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('bulletList') }"
          >
            <Icon icon="mdi:format-list-bulleted" class="h-4 w-4" />
          </button>
          
          <button
            @click="editor?.chain().focus().toggleOrderedList().run()"
            class="btn btn-sm btn-ghost"
            :class="{ 'btn-active': editor?.isActive('orderedList') }"
          >
            <Icon icon="mdi:format-list-numbered" class="h-4 w-4" />
          </button>
        </div>
        
        <div class="divider divider-horizontal"></div>
        
        <!-- Special Blocks -->
        <div class="dropdown">
          <label tabindex="0" class="btn btn-sm btn-primary">
            <Icon icon="mdi:plus" class="h-4 w-4 mr-1" />
            Insert
          </label>
          <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-64">
            <li class="menu-title">Assets</li>
            <li v-for="asset in assetStore.assets" :key="asset.id">
              <a @click="insertAssetBlock(asset.id)">
                <Icon :icon="getAssetIcon(asset.type)" class="h-4 w-4 mr-2" />
                {{ asset.name }}
              </a>
            </li>
            <li>
              <a @click="showNewAssetModal = true" class="text-primary">
                <Icon icon="mdi:plus" class="h-4 w-4" />
                Create New Asset
              </a>
            </li>
            <li v-if="assetStore.assets.length === 0">
              <span class="text-base-content/50">No assets added yet</span>
            </li>
            
            <li class="menu-title">Beneficiaries</li>
            <li v-for="beneficiary in beneficiaryStore.beneficiaries" :key="beneficiary.id">
              <a @click="insertBeneficiaryBlock(beneficiary.id)">
                <div class="flex items-center">
                  <div class="avatar mr-2">
                    <div class="w-6 h-6 rounded-full text-white flex items-center justify-center text-xs"
                         :class="{ 
                           'bg-amber-500': beneficiary.isDigitalExecutor,
                           'bg-secondary/20 text-secondary': !beneficiary.isDigitalExecutor 
                         }">
                      {{ getInitials(beneficiary.name) }}
                    </div>
                  </div>
                  <span>{{ beneficiary.name }}</span>
                  <span v-if="beneficiary.isDigitalExecutor" class="badge badge-warning badge-xs ml-1">
                    <Icon icon="mdi:crown" class="h-2 w-2" />
                  </span>
                </div>
              </a>
            </li>
            <li>
              <a @click="showNewBeneficiaryModal = true" class="text-primary">
                <Icon icon="mdi:plus" class="h-4 w-4" />
                Create New Beneficiary
              </a>
            </li>
            <li v-if="beneficiaryStore.beneficiaries.length === 0">
              <span class="text-base-content/50">No beneficiaries added yet</span>
            </li>
            
            <li class="menu-title">Instructions</li>
            <li><a @click="insertInstructionBlock('general')">General Instructions</a></li>
            <li><a @click="insertInstructionBlock('access')">Access Instructions</a></li>
            <li><a @click="insertInstructionBlock('security')">Security Instructions</a></li>
            <li><a @click="insertInstructionBlock('emergency')">Emergency Instructions</a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="bg-base-100 border-x border-b border-base-300 rounded-b-lg">
      <EditorContent 
        :editor="editor" 
        class="prose max-w-none p-6 min-h-[400px] focus:outline-none"
      />
    </div>
  </div>

  <!-- New Asset Modal -->
  <CreateAssetModal 
    :show="showNewAssetModal" 
    @close="confirmCloseAssetModal" 
    @created="handleNewAssetCreated" 
    :has-changes="hasAssetChanges"
  />

  <!-- New Beneficiary Modal -->
  <CreateBeneficiaryModal 
    :show="showNewBeneficiaryModal" 
    @close="confirmCloseBeneficiaryModal" 
    @created="handleNewBeneficiaryCreated"
    :has-changes="hasBeneficiaryChanges"
  />
</template>

<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3';
import { Icon } from '@iconify/vue';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import AssetBlockExtension from './extensions/AssetBlockExtension';
import BeneficiaryBlockExtension from './extensions/BeneficiaryBlockExtension';
import InstructionBlockExtension from './extensions/InstructionBlockExtension';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AssetType } from '~/types';
import CreateAssetModal from './CreateAssetModal.vue';
import CreateBeneficiaryModal from './CreateBeneficiaryModal.vue';

// Modal state
const showNewAssetModal = ref(false);
const showNewBeneficiaryModal = ref(false);
const hasAssetChanges = ref(false);
const hasBeneficiaryChanges = ref(false);

// Handle new asset created
const handleNewAssetCreated = (asset: any) => {
  showNewAssetModal.value = false;
  hasAssetChanges.value = false;
  insertAssetBlock(asset.id);
};

// Handle new beneficiary created
const handleNewBeneficiaryCreated = (beneficiary: any) => {
  showNewBeneficiaryModal.value = false;
  hasBeneficiaryChanges.value = false;
  insertBeneficiaryBlock(beneficiary.id);
};

// Confirm closing modals if there are unsaved changes
const confirmCloseAssetModal = (changes = false) => {
  hasAssetChanges.value = changes;
  if (changes) {
    if (confirm('You have unsaved changes. Are you sure you want to close?')) {
      showNewAssetModal.value = false;
      hasAssetChanges.value = false;
    }
  } else {
    showNewAssetModal.value = false;
  }
};

const confirmCloseBeneficiaryModal = (changes = false) => {
  hasBeneficiaryChanges.value = changes;
  if (changes) {
    if (confirm('You have unsaved changes. Are you sure you want to close?')) {
      showNewBeneficiaryModal.value = false;
      hasBeneficiaryChanges.value = false;
    }
  } else {
    showNewBeneficiaryModal.value = false;
  }
};

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
}>();

const emit = defineEmits(['update:modelValue']);

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

// Initialize TipTap editor
const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    Placeholder.configure({
      placeholder: props.placeholder || 'Start writing your will here...',
    }),
    Underline,
    AssetBlockExtension,
    BeneficiaryBlockExtension,
    InstructionBlockExtension,
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML());
  },
});

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newContent) => {
    const currentContent = editor.value?.getHTML();
    if (editor.value && newContent !== currentContent) {
      editor.value.commands.setContent(newContent, false);
    }
  }
);

// Helper functions
const getInitials = (name: string) => {
  if (!name) return '?';
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const getAssetIcon = (type: string) => {
  const icons: Record<string, string> = {
    'DIGITAL_ACCOUNT': 'mdi:monitor',
    'CRYPTOCURRENCY': 'mdi:bitcoin',
    'SOCIAL_MEDIA': 'mdi:account-group',
    'FINANCIAL_ACCOUNT': 'mdi:credit-card',
    'REAL_ESTATE': 'mdi:home',
    'VEHICLE': 'mdi:car',
    'COLLECTIBLE': 'mdi:treasure-chest',
    'INSURANCE': 'mdi:shield-check',
    'INTELLECTUAL_PROPERTY': 'mdi:lightbulb',
    'PERSONAL_BELONGING': 'mdi:bag-personal',
    'CUSTOM': 'mdi:tag'
  };

  return icons[type] || icons['CUSTOM'];
};

// Insert functions
const insertAssetBlock = (assetId: string) => {
  editor.value?.chain().focus().insertContent({
    type: 'assetBlock',
    attrs: { assetId: assetId }
  }).run();
};

const insertBeneficiaryBlock = (beneficiaryId: string) => {
  editor.value?.chain().focus().insertContent({
    type: 'beneficiaryBlock',
    attrs: { beneficiaryId: beneficiaryId }
  }).run();
};

const insertInstructionBlock = (type: string) => {
  editor.value?.chain().focus().insertContent({
    type: 'instructionBlock',
    attrs: { instructionType: type }
  }).run();
};

onBeforeUnmount(() => {
  editor.value?.destroy();
});
</script>

<style>
.ProseMirror:focus {
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Enhanced prose styling */
.prose h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  color: var(--tw-prose-headings);
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  color: var(--tw-prose-headings);
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0 0.5rem 0;
  color: var(--tw-prose-headings);
}

.prose p {
  margin: 0.75rem 0;
  line-height: 1.6;
}

.prose ul, .prose ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.prose li {
  margin: 0.25rem 0;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose u {
  text-decoration: underline;
}
</style>