<template>
  <div class="card bg-base-100 shadow-md transition-all hover:shadow-lg">
    <div class="card-body p-4">
      <div class="flex items-start gap-3">
        <div :class="`avatar bg-${typeColor}-100 p-2 rounded-full`">
          <div class="w-8 h-8 text-base-content flex items-center justify-center">
            <Icon :icon="typeIcon" class="w-5 h-5" />
          </div>
        </div>
        
        <div class="flex-1 min-w-0">
          <h3 class="font-semibold text-lg truncate">{{ asset.name }}</h3>
          <p class="text-sm text-base-content/70 mb-2">{{ assetTypeLabel }}</p>
          
          <div v-if="asset.value" class="badge badge-primary">
            {{ formatCurrency(asset.value, asset.currency || 'USD') }}
          </div>
          
          <div v-if="showBeneficiaryCount && asset.beneficiaries?.length" class="mt-2 text-sm">
            <span class="text-base-content/70">{{ asset.beneficiaries.length }} beneficiaries</span>
          </div>
        </div>
        
        <div class="dropdown dropdown-end">
          <label tabindex="0" class="btn btn-ghost btn-sm btn-circle">
            <Icon icon="mdi:dots-vertical" class="w-5 h-5" />
          </label>
          <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
            <li><a @click="$emit('edit', asset.id)">Edit Asset</a></li>
            <li><a @click="$emit('assign-beneficiaries', asset.id)">Quick Assign</a></li>
            <li><a @click="$emit('manage-beneficiaries', asset.id)">Manage Beneficiaries</a></li>
            <li><a @click="$emit('view-details', asset.id)">View Details</a></li>
            <li><a @click="$emit('delete', asset.id)" class="text-error">Delete Asset</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Icon } from '@iconify/vue';
import type { Asset, AssetType } from '~/types';

const props = defineProps<{
  asset: Asset;
  showBeneficiaryCount?: boolean;
}>();

defineEmits(['edit', 'assign-beneficiaries', 'manage-beneficiaries', 'view-details', 'delete']);

const assetTypeLabel = computed(() => {
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };
  
  return typeMap[props.asset.type] || 'Asset';
});

const typeColor = computed(() => {
  const colorMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'blue',
    CRYPTOCURRENCY: 'yellow',
    SOCIAL_MEDIA: 'purple',
    FINANCIAL_ACCOUNT: 'green',
    REAL_ESTATE: 'amber',
    VEHICLE: 'sky',
    COLLECTIBLE: 'pink',
    INSURANCE: 'emerald',
    INTELLECTUAL_PROPERTY: 'indigo',
    PERSONAL_BELONGING: 'orange',
    CUSTOM: 'gray'
  };
  
  return colorMap[props.asset.type] || 'blue';
});

const typeIcon = computed(() => {
  const iconMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'mdi:monitor',
    CRYPTOCURRENCY: 'mdi:bitcoin',
    SOCIAL_MEDIA: 'mdi:account-group',
    FINANCIAL_ACCOUNT: 'mdi:credit-card',
    REAL_ESTATE: 'mdi:home',
    VEHICLE: 'mdi:car',
    COLLECTIBLE: 'mdi:treasure-chest',
    INSURANCE: 'mdi:shield-check',
    INTELLECTUAL_PROPERTY: 'mdi:lightbulb',
    PERSONAL_BELONGING: 'mdi:bag-personal',
    CUSTOM: 'mdi:tag'
  };

  return iconMap[props.asset.type] || iconMap.CUSTOM;
});

// Helper function to format currency
const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};
</script>