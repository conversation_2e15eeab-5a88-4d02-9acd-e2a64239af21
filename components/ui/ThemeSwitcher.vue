<template>
  <div class="dropdown dropdown-end">
    <label tabindex="0" class="btn btn-ghost btn-circle">
      <Icon icon="mdi:theme-light-dark" class="h-5 w-5" />
    </label>
    <ul tabindex="0" class="dropdown-content z-10 menu p-2 shadow bg-base-100 rounded-box w-36">
      <li v-for="theme in themes" :key="theme.value">
        <a @click.prevent="setTheme(theme.value)">
          <Icon v-if="theme.value === 'garden'" icon="mdi:weather-sunny" class="inline h-4 w-4 mr-2" />
          <Icon v-else-if="theme.value === 'forest'" icon="mdi:weather-night" class="inline h-4 w-4 mr-2" />
          {{ theme.label }}
        </a>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { Icon } from '@iconify/vue';

const themes = [
  { label: 'Garden (Light)', value: 'garden' },
  { label: 'Forest (Dark)', value: 'forest' }
];

const THEME_KEY = 'dw_theme';

const setTheme = (theme: string) => {
   document.documentElement.setAttribute('data-theme', theme);
  try {
    localStorage.setItem(THEME_KEY, theme);
  } catch (error) {
    console.warn('Failed to save theme preference:', error);
  }
 };

const initTheme = () => {
  // Guard against SSR
  if (process.client) {
    try {
      const stored = localStorage.getItem(THEME_KEY);
      let prefersDark = false;
      if (typeof window !== 'undefined' && window.matchMedia) {
        prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      }
      if (stored && themes.some(t => t.value === stored)) {
        setTheme(stored);
      } else {
        setTheme(prefersDark ? 'forest' : 'garden');
      }
    } catch (error) {
      console.warn('Failed to initialize theme:', error);
      setTheme('garden'); // fallback to light theme
    }
  }
 };

onMounted(() => {
  initTheme();
});
</script> 