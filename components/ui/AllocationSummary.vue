<template>
  <div class="card bg-base-100 shadow-md">
    <div class="card-body">
      <h3 class="text-lg font-semibold mb-4">Allocation Summary</h3>
      
      <!-- Overall Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-base-200 rounded-lg p-3">
          <div class="stat-title text-xs">Total Assets</div>
          <div class="stat-value text-lg">{{ totalAssets }}</div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg p-3">
          <div class="stat-title text-xs">Fully Allocated</div>
          <div class="stat-value text-lg text-success">{{ fullyAllocatedCount }}</div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg p-3">
          <div class="stat-title text-xs">Partially Allocated</div>
          <div class="stat-value text-lg text-warning">{{ partiallyAllocatedCount }}</div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg p-3">
          <div class="stat-title text-xs">Unallocated</div>
          <div class="stat-value text-lg text-error">{{ unallocatedCount }}</div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mb-4">
        <div class="flex justify-between text-sm mb-1">
          <span>Overall Allocation Progress</span>
          <span>{{ Math.round(allocationPercentage) }}%</span>
        </div>
        <div class="w-full bg-base-300 rounded-full h-2">
          <div 
            class="h-2 rounded-full transition-all duration-300"
            :class="progressBarClass"
            :style="{ width: `${allocationPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- Asset Status List -->
      <div v-if="showDetails" class="space-y-2">
        <h4 class="font-medium text-sm">Asset Details</h4>
        
        <!-- Unallocated Assets -->
        <div v-if="unallocatedAssets.length > 0">
          <h5 class="text-xs font-medium text-error mb-2">Unallocated Assets</h5>
          <div class="space-y-1">
            <div 
              v-for="asset in unallocatedAssets" 
              :key="asset.id"
              class="flex items-center justify-between p-2 bg-error/10 rounded text-sm"
            >
              <span>{{ asset.name }}</span>
              <button 
                @click="$emit('assign-asset', asset.id)"
                class="btn btn-xs btn-error btn-outline"
              >
                Assign
              </button>
            </div>
          </div>
        </div>

        <!-- Partially Allocated Assets -->
        <div v-if="partiallyAllocatedAssets.length > 0">
          <h5 class="text-xs font-medium text-warning mb-2">Partially Allocated Assets</h5>
          <div class="space-y-1">
            <div 
              v-for="asset in partiallyAllocatedAssets" 
              :key="asset.id"
              class="flex items-center justify-between p-2 bg-warning/10 rounded text-sm"
            >
              <div>
                <div>{{ asset.name }}</div>
                <div class="text-xs text-base-content/70">
                  {{ getAssetAllocation(asset.id) }}% allocated
                </div>
              </div>
              <button 
                @click="$emit('manage-asset', asset.id)"
                class="btn btn-xs btn-warning btn-outline"
              >
                Complete
              </button>
            </div>
          </div>
        </div>

        <!-- Fully Allocated Assets -->
        <div v-if="fullyAllocatedAssets.length > 0 && showFullyAllocated">
          <h5 class="text-xs font-medium text-success mb-2">Fully Allocated Assets</h5>
          <div class="space-y-1">
            <div 
              v-for="asset in fullyAllocatedAssets" 
              :key="asset.id"
              class="flex items-center justify-between p-2 bg-success/10 rounded text-sm"
            >
              <div>
                <div>{{ asset.name }}</div>
                <div class="text-xs text-base-content/70">100% allocated</div>
              </div>
              <button 
                @click="$emit('manage-asset', asset.id)"
                class="btn btn-xs btn-success btn-outline"
              >
                Manage
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Toggle Details -->
      <div class="mt-4">
        <button 
          @click="showDetails = !showDetails"
          class="btn btn-sm btn-ghost w-full"
        >
          {{ showDetails ? 'Hide Details' : 'Show Details' }}
          <Icon
            name="mdi:chevron-down"
            class="h-4 w-4 ml-1 transition-transform"
            :class="{ 'rotate-180': showDetails }"
          />
        </button>
      </div>

      <!-- Action Buttons -->
      <div v-if="showActions" class="mt-4 flex gap-2">
        <button 
          v-if="unallocatedAssets.length > 0"
          @click="$emit('auto-allocate')"
          class="btn btn-sm btn-primary flex-1"
        >
          Auto-Allocate Remaining
        </button>
        
        <button 
          @click="$emit('view-report')"
          class="btn btn-sm btn-outline flex-1"
        >
          View Full Report
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Icon } from '@iconify/vue';
import type { Asset } from '~/types';
import { 
  calculateAssetAllocation, 
  getUnallocatedAssets, 
  getPartiallyAllocatedAssets,
  type AssetAssignment 
} from '~/utils/beneficiaryAssignments';

const props = defineProps<{
  assets: Asset[];
  assignments: AssetAssignment[];
  showActions?: boolean;
  showFullyAllocated?: boolean;
}>();

defineEmits(['assign-asset', 'manage-asset', 'auto-allocate', 'view-report']);

const showDetails = ref(false);

// Computed properties
const totalAssets = computed(() => props.assets.length);

const unallocatedAssets = computed(() => 
  getUnallocatedAssets(props.assets, props.assignments)
);

const partiallyAllocatedAssets = computed(() => 
  getPartiallyAllocatedAssets(props.assets, props.assignments)
);

const fullyAllocatedAssets = computed(() => 
  props.assets.filter(asset => {
    const summary = calculateAssetAllocation(asset.id, props.assignments);
    return summary.isComplete;
  })
);

const unallocatedCount = computed(() => unallocatedAssets.value.length);
const partiallyAllocatedCount = computed(() => partiallyAllocatedAssets.value.length);
const fullyAllocatedCount = computed(() => fullyAllocatedAssets.value.length);

const allocationPercentage = computed(() => {
  if (totalAssets.value === 0) return 0;
  return (fullyAllocatedCount.value / totalAssets.value) * 100;
});

const progressBarClass = computed(() => {
  if (allocationPercentage.value === 100) return 'bg-success';
  if (allocationPercentage.value >= 75) return 'bg-info';
  if (allocationPercentage.value >= 50) return 'bg-warning';
  return 'bg-error';
});

// Helper functions
const getAssetAllocation = (assetId: string) => {
  const summary = calculateAssetAllocation(assetId, props.assignments);
  return summary.totalPercentage;
};
</script>
