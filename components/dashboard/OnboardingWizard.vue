<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[400px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <span class="font-bold text-lg">Get Started</span>
          <span class="badge badge-primary">Step {{ step + 1 }}/4</span>
        </div>
        <button class="btn btn-xs btn-ghost" @click="dismiss">✕</button>
      </div>
      <div class="mb-4">
        <template v-if="step === 0">
          <h2 class="font-semibold mb-1">Complete your profile</h2>
          <p class="text-sm text-base-content/70 mb-2">Add your name and contact info to personalize your experience.</p>
          <button class="btn btn-primary btn-sm w-full" @click="goToProfile">Go to Profile</button>
        </template>
        <template v-else-if="step === 1">
          <h2 class="font-semibold mb-1">Add your first asset</h2>
          <p class="text-sm text-base-content/70 mb-2">Document a digital, financial, or physical asset you want to include in your will.</p>
          <button class="btn btn-primary btn-sm w-full" @click="goToAssets">Add Asset</button>
        </template>
        <template v-else-if="step === 2">
          <h2 class="font-semibold mb-1">Add a beneficiary</h2>
          <p class="text-sm text-base-content/70 mb-2">Assign someone to receive your assets.</p>
          <button class="btn btn-primary btn-sm w-full" @click="goToBeneficiaries">Add Beneficiary</button>
        </template>
        <template v-else-if="step === 3">
          <h2 class="font-semibold mb-1">Create your will document</h2>
          <p class="text-sm text-base-content/70 mb-2">Start your digital will and specify your wishes.</p>
          <button class="btn btn-primary btn-sm w-full" @click="goToWill">Create Will</button>
        </template>
      </div>
      <div class="flex justify-between items-center mt-2">
        <div class="flex gap-1">
          <span v-for="i in 4" :key="i" class="w-2 h-2 rounded-full" :class="i-1 <= step ? 'bg-primary' : 'bg-base-300'"></span>
        </div>
        <button v-if="step < 3 && canContinue" class="btn btn-xs btn-outline" @click="nextStep">Next</button>
        <button v-else-if="step === 3 && canContinue" class="btn btn-xs btn-success" @click="finish">Finish</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useUserStore } from '~/stores/userStore';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useWillStore } from '~/stores/willStore';
import { useRouter } from 'vue-router';

const emit = defineEmits(['finished', 'dismissed']);
const show = ref(true);
const step = ref(0);
const router = useRouter();

const userStore = useUserStore();
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();
const willStore = useWillStore();

const canContinue = computed(() => {
  if (step.value === 0) return userStore.isProfileComplete;
  if (step.value === 1) return assetStore.assets.length > 0;
  if (step.value === 2) return beneficiaryStore.beneficiaries.length > 0;
  if (step.value === 3) return !!willStore.willDocument;
  return false;
});

const nextStep = () => {
  if (step.value < 3 && canContinue.value) step.value++;
};
const finish = () => {
  show.value = false;
  emit('finished');
};
const dismiss = () => {
  show.value = false;
  emit('dismissed');
};
const goToProfile = () => router.push('/profile');
const goToAssets = () => router.push('/dashboard/assets');
const goToBeneficiaries = () => router.push('/dashboard/beneficiaries');
const goToWill = () => router.push('/dashboard/will');

// Only show for first-time users (localStorage flag)
onMounted(() => {
  if (typeof window !== 'undefined') {
    if (localStorage.getItem('onboarding_complete')) {
      show.value = false;
    }
  }
});
watch(
  () => step.value,
  (val) => {
    if (val === 3 && canContinue.value) {
      localStorage.setItem('onboarding_complete', '1');
    }
  }
);
</script> 