<template>
  <div class="card bg-base-100 shadow-md h-full">
    <div class="card-body p-4 flex flex-col">
      <div class="flex justify-between items-start mb-4">
        <div>
          <h3 class="card-title text-lg">{{ title }}</h3>
          <p class="text-sm text-base-content/70">{{ description }}</p>
        </div>
        <div 
          class="radial-progress text-primary" 
          :style="`--value: ${percentage}; --size: 2.5rem; --thickness: 0.375rem;`"
        >{{ percentage }}%</div>
      </div>
      
      <ul class="mt-2 space-y-2">
        <li v-for="item in items" :key="item.id" class="flex items-center">
          <div class="form-control mr-2">
            <input 
              type="checkbox" 
              :checked="item.completed" 
              class="checkbox checkbox-sm checkbox-primary" 
              @change="toggleItem(item.id)"
            />
          </div>
          <span :class="{ 'line-through text-base-content/50': item.completed }">{{ item.text }}</span>
        </li>
      </ul>
      
      <div class="card-actions mt-auto justify-end">
        <NuxtLink :to="actionLink" class="btn btn-sm btn-primary">
          {{ actionText }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title: string;
  description: string;
  percentage: number;
  items: Array<{
    id: string;
    text: string;
    completed: boolean;
  }>;
  actionText: string;
  actionLink: string;
}>();

const emit = defineEmits(['toggle-item']);

const toggleItem = (id: string) => {
  emit('toggle-item', id);
};
</script>