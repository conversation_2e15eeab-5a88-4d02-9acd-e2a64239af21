import { z } from 'zod';
import type { AssetType, AccessLevel, NotificationPreference } from '~/types';

/**
 * Validation schemas for forms using Zod
 */

// Asset validation schema
export const assetSchema = z.object({
  name: z.string().min(1, 'Asset name is required'),
  type: z.string().min(1, 'Asset type is required'),
  description: z.string().optional(),
  value: z.number().optional(),
  currency: z.string().default('USD'),
  location: z.string().optional(),
  accountDetails: z.object({
    provider: z.string().optional(),
    accountNumber: z.string().optional(),
    username: z.string().optional(),
    url: z.string().optional(),
    notes: z.string().optional(),
  }).optional(),
});

// Beneficiary validation schema
export const beneficiarySchema = z.object({
  name: z.string().min(1, 'Beneficiary name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().optional(),
  relationship: z.string().optional(),
  accessLevel: z.string().min(1, 'Access level is required'),
  notificationPreference: z.string().min(1, 'Notification preference is required'),
});

// Security question validation
export const securityQuestionSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  answer: z.string().min(1, 'Answer is required'),
});

// Trusted contact validation schema
export const trustedContactSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().optional(),
  relationship: z.string().optional(),
});

// Will document validation schema
export const willDocumentSchema = z.object({
  title: z.string().min(1, 'Document title is required'),
  content: z.string().min(1, 'Document content is required'),
});

// Trigger settings validation schema
export const triggerSettingsSchema = z.object({
  inactivityPeriod: z.number().min(30, 'Inactivity period must be at least 30 days'),
  reminderFrequency: z.number().min(7, 'Reminder frequency must be at least 7 days'),
  verificationMethod: z.string().min(1, 'Verification method is required'),
  isActive: z.boolean(),
});

// User profile validation schema
export const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phoneNumber: z.string().optional(),
});

// Password validation schema
export const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});