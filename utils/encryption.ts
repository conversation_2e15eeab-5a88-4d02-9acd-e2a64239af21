import CryptoJS from 'crypto-js';

/**
 * Utility for encrypting and decrypting sensitive data
 */

/**
 * Encrypts data using AES encryption
 * @param data Data to encrypt
 * @param key Encryption key (user-specific)
 * @returns Object containing encrypted data and IV
 */
export function encryptData(data: string, key: string) {
  // Generate a random IV
  const iv = CryptoJS.lib.WordArray.random(16);
  
  // Encrypt the data
  const encrypted = CryptoJS.AES.encrypt(data, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  
  return {
    encryptedData: encrypted.toString(),
    iv: iv.toString()
  };
}

/**
 * Decrypts data using AES encryption
 * @param encryptedData Encrypted data string
 * @param iv Initialization vector used for encryption
 * @param key Encryption key (user-specific)
 * @returns Decrypted data as string
 */
export function decryptData(encryptedData: string, iv: string, key: string) {
  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
    iv: CryptoJS.enc.Hex.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  
  return decrypted.toString(CryptoJS.enc.Utf8);
}

/**
 * Generates a user-specific encryption key based on their ID and a secret
 * @param userId User ID
 * @returns Encryption key
 */
export function generateUserKey(userId: string) {
  // In a real app, this would use a more secure approach with server-side components
  // This is a simplified implementation for demo purposes
  const salt = process.env.ENCRYPTION_SALT || 'default-salt-value';
  return CryptoJS.PBKDF2(userId, salt, { keySize: 256/32, iterations: 1000 }).toString();
}

/**
 * Creates a secure hash of data (for security questions, etc)
 * @param data Data to hash
 * @param salt Salt to use for hashing
 * @returns Hashed data
 */
export function hashData(data: string, salt: string) {
  return CryptoJS.SHA256(data + salt).toString();
}