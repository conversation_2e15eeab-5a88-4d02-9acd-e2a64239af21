import type { Asset, Beneficiary } from '~/types';

export interface AssetAssignment {
  assetId: string;
  beneficiaryId: string;
  percentage: number;
}

export interface AssignmentSummary {
  totalPercentage: number;
  remainingPercentage: number;
  isValid: boolean;
  isComplete: boolean;
}

/**
 * Calculate the total percentage allocated for a specific asset
 */
export function calculateAssetAllocation(assetId: string, assignments: AssetAssignment[]): AssignmentSummary {
  const assetAssignments = assignments.filter(a => a.assetId === assetId);
  const totalPercentage = assetAssignments.reduce((sum, assignment) => sum + assignment.percentage, 0);
  
  // Round to 2 decimal places to avoid floating-point precision issues
  const roundedTotal = Math.round(totalPercentage * 100) / 100;
  
  return {
    totalPercentage: roundedTotal,
    remainingPercentage: Math.max(0, Math.round((100 - roundedTotal) * 100) / 100),
    isValid: roundedTotal <= 100,
    isComplete: Math.abs(roundedTotal - 100) < 0.01 // Allow small rounding errors
  };
}

/**
 * Calculate the total inheritance value for a beneficiary
 */
export function calculateBeneficiaryInheritance(
  beneficiaryId: string, 
  assignments: AssetAssignment[], 
  assets: Asset[]
): number {
  const beneficiaryAssignments = assignments.filter(a => a.beneficiaryId === beneficiaryId);
  
  return beneficiaryAssignments.reduce((total, assignment) => {
    const asset = assets.find(a => a.id === assignment.assetId);
    if (asset?.value) {
      return total + (asset.value * assignment.percentage / 100);
    }
    return total;
  }, 0);
}

/**
 * Validate that an assignment doesn't exceed 100% for an asset
 */
export function validateAssignment(
  assetId: string, 
  beneficiaryId: string, 
  percentage: number, 
  existingAssignments: AssetAssignment[]
): { isValid: boolean; message?: string } {
  if (!Number.isFinite(percentage) || percentage < 1 || percentage > 100) {
    return { isValid: false, message: 'Percentage must be between 1 and 100' };
  }

  const otherAssignments = existingAssignments.filter(
    a => a.assetId === assetId && a.beneficiaryId !== beneficiaryId
  );
  
  const otherTotal = otherAssignments.reduce((sum, a) => sum + a.percentage, 0);
  
  if (otherTotal + percentage > 100) {
    return { 
      isValid: false, 
      message: `This would exceed 100% allocation. Maximum allowed: ${100 - otherTotal}%` 
    };
  }

  return { isValid: true };
}

/**
 * Get suggested percentage for a new assignment
 */
export function getSuggestedPercentage(assetId: string, assignments: AssetAssignment[]): number {
  const summary = calculateAssetAllocation(assetId, assignments);
  
  // If no remaining percentage, return 0
  if (summary.remainingPercentage === 0) {
    return 0;
  }
  
  if (summary.remainingPercentage >= 25) {
    return 25;
  } else if (summary.remainingPercentage >= 10) {
    return 10;
  } else if (summary.remainingPercentage > 0) {
    return summary.remainingPercentage;
  }
  
  return 0; // No allocation possible
}

/**
 * Format currency value
 */
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
}

/**
 * Get asset assignments for a specific beneficiary
 */
export function getBeneficiaryAssignments(
  beneficiaryId: string, 
  assignments: AssetAssignment[]
): AssetAssignment[] {
  return assignments.filter(a => a.beneficiaryId === beneficiaryId);
}

/**
 * Get beneficiary assignments for a specific asset
 */
export function getAssetAssignments(
  assetId: string, 
  assignments: AssetAssignment[]
): AssetAssignment[] {
  return assignments.filter(a => a.assetId === assetId);
}

/**
 * Check if all assets are fully allocated
 */
export function areAllAssetsAllocated(assets: Asset[], assignments: AssetAssignment[]): boolean {
  return assets.every(asset => {
    const summary = calculateAssetAllocation(asset.id, assignments);
    return summary.isComplete;
  });
}

/**
 * Get unallocated assets
 */
export function getUnallocatedAssets(assets: Asset[], assignments: AssetAssignment[]): Asset[] {
  return assets.filter(asset => {
    const summary = calculateAssetAllocation(asset.id, assignments);
    return summary.totalPercentage === 0;
  });
}

/**
 * Get partially allocated assets
 */
export function getPartiallyAllocatedAssets(assets: Asset[], assignments: AssetAssignment[]): Asset[] {
  return assets.filter(asset => {
    const summary = calculateAssetAllocation(asset.id, assignments);
    return summary.totalPercentage > 0 && summary.totalPercentage < 100;
  });
}
