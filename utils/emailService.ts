export interface EmailRecipient {
  email: string;
  name: string;
}

export interface TriggerAlertOptions {
  recipients: EmailRecipient[];
  alertType: 'inactivity_warning' | 'verification_request';
  userName: string;
  userEmail: string;
  daysInactive?: number;
  customMessage?: string;
}

export class EmailService {
  /**
   * Send welcome signup email
   */
  async sendWelcomeSignupEmail(
    userEmail: string,
    userName: string,
    confirmationUrl?: string
  ) {
    try {
      const response = await $fetch('/api/send-email', {
        method: 'POST',
        body: {
          type: 'welcome-signup',
          userEmail,
          userName,
          confirmationUrl
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Error sending welcome signup email:', error);
      return { success: false, error };
    }
  }

  /**
   * Send beneficiary notification email
   */
  async sendBeneficiaryNotification(
    beneficiaryEmail: string,
    beneficiaryName: string,
    userEmail: string,
    userName: string
  ) {
    try {
      const response = await $fetch('/api/send-email', {
        method: 'POST',
        body: {
          type: 'beneficiary-notification',
          beneficiaryEmail,
          beneficiaryName,
          userEmail,
          userName
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Error sending beneficiary notification:', error);
      return { success: false, error };
    }
  }

  /**
   * Send digital executor notification email
   */
  async sendDigitalExecutorNotification(
    executorEmail: string,
    executorName: string,
    userEmail: string,
    userName: string
  ) {
    try {
      const response = await $fetch('/api/send-email', {
        method: 'POST',
        body: {
          type: 'digital-executor-notification',
          executorEmail,
          executorName,
          userEmail,
          userName
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Error sending digital executor notification:', error);
      return { success: false, error };
    }
  }

  /**
   * Send trusted contact notification email
   */
  async sendTrustedContactNotification(
    contactEmail: string,
    contactName: string,
    userEmail: string,
    userName: string
  ) {
    try {
      const response = await $fetch('/api/send-email', {
        method: 'POST',
        body: {
          type: 'trusted-contact-notification',
          contactEmail,
          contactName,
          userEmail,
          userName
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Error sending trusted contact notification:', error);
      return { success: false, error };
    }
  }

  /**
   * Send trigger alert emails
   */
  async sendTriggerAlert(options: TriggerAlertOptions) {
    try {
      const response = await $fetch('/api/send-email', {
        method: 'POST',
        body: {
          type: 'trigger-alert',
          ...options
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Error sending trigger alert:', error);
      return { success: false, error };
    }
  }

  /**
   * Helper method to send inactivity warnings
   */
  async sendInactivityWarning(
    recipients: EmailRecipient[],
    userName: string,
    userEmail: string,
    daysInactive: number,
    customMessage?: string
  ) {
    return this.sendTriggerAlert({
      recipients,
      alertType: 'inactivity_warning',
      userName,
      userEmail,
      daysInactive,
      customMessage
    });
  }

  /**
   * Helper method to send verification requests
   */
  async sendVerificationRequest(
    recipients: EmailRecipient[],
    userName: string,
    userEmail: string,
    customMessage?: string
  ) {
    return this.sendTriggerAlert({
      recipients,
      alertType: 'verification_request',
      userName,
      userEmail,
      customMessage
    });
  }
}

/**
 * Create email service instance
 */
export function createEmailService(): EmailService {
  return new EmailService();
}

/**
 * Composable for email service
 * Use this in Vue components and composables
 */
export function useEmailService(): EmailService {
  return createEmailService();
}