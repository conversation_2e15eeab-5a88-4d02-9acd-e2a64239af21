@import "tailwindcss";
@plugin "daisyui" {
   themes: garden --default, forest --prefersdark;
 }

/* Add these styles for the editor components */
.asset-block, .beneficiary-block, .instruction-block {
  position: relative;
}

.asset-block:hover::after,
.beneficiary-block:hover::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 2px dashed var(--fallback-p, oklch(var(--p)));
  border-radius: 0.5rem;
  pointer-events: none;
}

.instruction-block:hover {
  box-shadow: 0 0 0 2px var(--fallback-b2, oklch(var(--b2))), 0 0 0 4px var(--fallback-p, oklch(var(--p)));
}

/* Animation for modals */
.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
