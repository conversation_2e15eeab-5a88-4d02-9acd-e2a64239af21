// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  modules: [
    '@nuxtjs/supabase',
    '@pinia/nuxt'
  ],
  vite: {
    plugins: [tailwindcss()],
  },
  nitro: {
    experimental: {
      wasm: true
    },
    esbuild: {
      options: {
        target: 'esnext'
      }
    }
  },
  css: ["~/assets/css/main.css"],
  runtimeConfig: {
    resendApiKey: process.env.RESEND_API_KEY,
    picaApiKey: process.env.PICA_API_KEY,
    public: {
      posthogPublicKey: process.env.POSTHOG_API_KEY,
      posthogHost: process.env.POSTHOG_HOST
    }
  },
  supabase: {
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_ANON_KEY,
    redirectOptions: {
      login: '/auth/login',
      callback: '/auth/confirm',
      exclude: ['/', '/auth/register', '/about']
    }
  },
  app: {
    head: {
      title: 'Volnt - Digital Will & Testament',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'Volnt - Secure digital will and testament application for managing your digital legacy' }
      ],
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/images/volnt.svg' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' }
      ]
    }
  },
  routeRules: {
    '/relay-NB0B/static/**': { proxy: 'https://us-assets.i.posthog.com/static/**' },
    '/relay-NB0B/**': { proxy: 'https://us.i.posthog.com/**' },
  },
})