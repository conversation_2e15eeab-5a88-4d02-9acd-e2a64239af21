<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Welcome Back to Volnt</h1>
    
    <form @submit.prevent="handleLogin" class="space-y-4">
      <div v-if="errorMessage" class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>{{ errorMessage }}</span>
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Email</span>
        </label>
        <input 
          type="email" 
          v-model="email"
          class="input input-bordered w-full" 
          placeholder="<EMAIL>"
          required
        />
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Password</span>
        </label>
        <input 
          type="password" 
          v-model="password"
          class="input input-bordered w-full" 
          placeholder="••••••••"
          required
        />
        <label class="label">
          <NuxtLink to="/auth/reset-password" class="label-text-alt link link-hover text-primary">
            Forgot password?
          </NuxtLink>
        </label>
      </div>
      
      <div class="form-control">
        <label class="label cursor-pointer">
          <span class="label-text">Remember me</span> 
          <input type="checkbox" v-model="rememberMe" class="checkbox checkbox-primary" />
        </label>
      </div>
      
      <button 
        type="submit" 
        class="btn btn-primary w-full" 
        :class="{ 'loading': isLoading }"
        :disabled="isLoading"
      >
        {{ isLoading ? 'Logging in...' : 'Login' }}
      </button>
      
      <p class="text-center mt-4 text-sm">
        Don't have an account? 
        <NuxtLink to="/auth/register" class="text-primary font-medium">
          Sign up
        </NuxtLink>
      </p>
    </form>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'auth'
});

const supabase = useSupabaseClient();
const router = useRouter();
const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const isLoading = ref(false);
const errorMessage = ref('');

const handleLogin = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  
  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: email.value,
      password: password.value
    });
    
    if (error) throw error;
    
    // Redirect to dashboard
    router.push('/dashboard');
  } catch (error: any) {
    errorMessage.value = error.message || 'Failed to sign in';
  } finally {
    isLoading.value = false;
  }
};

const googleSignIn = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    });
    
    if (error) throw error;
    
  } catch (error: any) {
    errorMessage.value = error.message || 'Failed to sign in with Google';
    isLoading.value = false;
  }
};
</script>