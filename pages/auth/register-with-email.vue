<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Create Your Volnt Account</h1>
    <p class="text-sm text-gray-600 mb-6">This version includes automated welcome emails</p>
    
    <form @submit.prevent="handleRegister" class="space-y-4">
      <div v-if="errorMessage" class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ errorMessage }}</span>
      </div>

      <div v-if="successMessage" class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ successMessage }}</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">First Name</span>
          </label>
          <input 
            type="text" 
            v-model="firstName" 
            placeholder="Enter your first name" 
            class="input input-bordered" 
            required 
          />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">Last Name</span>
          </label>
          <input 
            type="text" 
            v-model="lastName" 
            placeholder="Enter your last name" 
            class="input input-bordered" 
            required 
          />
        </div>
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Email</span>
        </label>
        <input 
          type="email" 
          v-model="email" 
          placeholder="Enter your email" 
          class="input input-bordered" 
          required 
        />
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Password</span>
        </label>
        <div class="relative">
          <input 
            :type="showPassword ? 'text' : 'password'" 
            v-model="password" 
            placeholder="Create a strong password" 
            class="input input-bordered w-full pr-10" 
            required 
          />
          <button 
            type="button" 
            @click="showPassword = !showPassword"
            class="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.414-1.414M14.12 14.12l1.414 1.414M14.12 14.12L15.536 15.536M14.12 14.12l1.414 1.414" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Email Options -->
      <div class="form-control">
        <label class="label cursor-pointer">
          <span class="label-text">Send welcome email</span>
          <input type="checkbox" v-model="sendWelcomeEmail" class="checkbox checkbox-primary" />
        </label>
      </div>
      
      <div class="form-control">
        <label class="label cursor-pointer">
          <span class="label-text">I agree to the <NuxtLink to="/terms">Terms of Service</NuxtLink> and <NuxtLink to="/privacy" class="link link-primary">Privacy Policy</NuxtLink></span> 
          <input type="checkbox" v-model="agreeToTerms" class="checkbox checkbox-primary" required />
        </label>
      </div>
      
      <button 
        type="submit" 
        class="btn btn-primary w-full" 
        :class="{ 'loading': isLoading }"
        :disabled="isLoading || !isPasswordValid || !agreeToTerms"
      >
        {{ isLoading ? 'Creating account...' : 'Create Account' }}
      </button>
      
      <p class="text-center mt-4 text-sm">
        Already have an account? 
        <NuxtLink to="/auth/login" class="text-primary font-medium">
          Login
        </NuxtLink>
      </p>
    </form>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod';

definePageMeta({
  layout: 'auth'
});

const supabase = useSupabaseClient();
const router = useRouter();
const { handleNewUserSignup } = useAuthEmails();

const firstName = ref('');
const lastName = ref('');
const email = ref('');
const password = ref('');
const showPassword = ref(false);
const agreeToTerms = ref(false);
const sendWelcomeEmail = ref(true);
const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// Password validation
const hasMinLength = computed(() => password.value.length >= 8);
const hasUpperCase = computed(() => /[A-Z]/.test(password.value));
const hasNumber = computed(() => /[0-9]/.test(password.value));
const isPasswordValid = computed(() => 
  hasMinLength.value && hasUpperCase.value && hasNumber.value
);

// Validation schema using Zod
const userSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain an uppercase letter')
    .regex(/[0-9]/, 'Password must contain a number'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  agreeToTerms: z.literal(true, {
    errorMap: () => ({ message: 'You must agree to the terms' }),
  })
});

const handleRegister = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  successMessage.value = '';
  
  try {
    // Validate inputs
    const validatedData = userSchema.parse({
      email: email.value,
      password: password.value,
      firstName: firstName.value,
      lastName: lastName.value,
      agreeToTerms: agreeToTerms.value
    });
    
    // Register with Supabase
    const { data, error } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        data: {
          first_name: validatedData.firstName,
          last_name: validatedData.lastName,
          full_name: `${validatedData.firstName} ${validatedData.lastName}`
        }
      }
    });
    
    if (error) throw error;
    
    // Send welcome email if enabled and user was created
    if (sendWelcomeEmail.value && data.user) {
      try {
        const emailResult = await handleNewUserSignup(data.user, {
          sendWelcomeEmail: true,
          includeConfirmationUrl: !data.user.email_confirmed_at
        });
        
        if (emailResult.success) {
          successMessage.value = 'Account created successfully! Welcome email sent.';
        } else {
          successMessage.value = 'Account created successfully! (Welcome email failed to send)';
        }
      } catch (emailError) {
        console.error('Welcome email error:', emailError);
        successMessage.value = 'Account created successfully! (Welcome email failed to send)';
      }
    } else {
      successMessage.value = 'Account created successfully!';
    }
    
    // Redirect to dashboard after a short delay
    setTimeout(() => {
      router.push('/dashboard');
    }, 2000);
    
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      errorMessage.value = error.errors[0].message;
    } else {
      errorMessage.value = error.message || 'Failed to create account';
    }
  } finally {
    isLoading.value = false;
  }
};
</script>
