<template>
  <div class="min-h-screen flex items-center justify-center bg-base-200">
    <div class="card w-full max-w-md bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title text-center mb-6">Send Welcome Email</h2>
        
        <form @submit.prevent="sendWelcomeEmail" class="space-y-4">
          <div v-if="errorMessage" class="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ errorMessage }}</span>
          </div>

          <div v-if="successMessage" class="alert alert-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ successMessage }}</span>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">User Name</span>
            </label>
            <input 
              type="text" 
              v-model="userName" 
              placeholder="Enter user's full name" 
              class="input input-bordered" 
              required 
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Email Address</span>
            </label>
            <input 
              type="email" 
              v-model="userEmail" 
              placeholder="Enter user's email" 
              class="input input-bordered" 
              required 
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Confirmation URL (optional)</span>
            </label>
            <input 
              type="url" 
              v-model="confirmationUrl" 
              placeholder="https://volnt.xyz/auth/confirm?token=..." 
              class="input input-bordered" 
            />
            <label class="label">
              <span class="label-text-alt">Leave empty if email is already confirmed</span>
            </label>
          </div>
          
          <button 
            type="submit" 
            class="btn btn-primary w-full" 
            :class="{ 'loading': isLoading }"
            :disabled="isLoading"
          >
            {{ isLoading ? 'Sending...' : 'Send Welcome Email' }}
          </button>
        </form>

        <div class="divider">Preview</div>
        
        <div class="mockup-browser border bg-base-300">
          <div class="mockup-browser-toolbar">
            <div class="input">Email Preview</div>
          </div>
          <div class="flex justify-center px-4 py-8 bg-gray-100">
            <div class="w-full max-w-sm bg-white rounded-lg shadow-lg overflow-hidden">
              <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 text-center">
                <div class="text-2xl mb-2">🛡️</div>
                <h3 class="font-semibold">Welcome to Volnt</h3>
              </div>
              <div class="p-4">
                <h4 class="font-medium mb-2">Hi {{ userName || 'User' }}!</h4>
                <div class="bg-blue-50 border-l-4 border-blue-500 p-2 mb-3 text-xs">
                  <strong>Your digital legacy is now protected.</strong>
                </div>
                <div class="space-y-1 text-xs mb-3">
                  <div class="flex items-center">
                    <span class="mr-2">📝</span>
                    <span>Create your digital will</span>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-2">🔒</span>
                    <span>Secure asset management</span>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-2">👥</span>
                    <span>Add trusted contacts</span>
                  </div>
                </div>
                <button class="w-full bg-blue-600 text-white py-2 px-4 rounded text-xs">
                  {{ confirmationUrl ? 'Confirm Your Email' : 'Get Started' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'auth'
});

const userName = ref('');
const userEmail = ref('');
const confirmationUrl = ref('');
const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

const sendWelcomeEmail = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  successMessage.value = '';
  
  try {
    const response = await $fetch('/api/send-email', {
      method: 'POST',
      body: {
        type: 'welcome-signup',
        userEmail: userEmail.value,
        userName: userName.value,
        confirmationUrl: confirmationUrl.value || undefined
      }
    });
    
    successMessage.value = 'Welcome email sent successfully!';
    
    // Reset form after success
    setTimeout(() => {
      userName.value = '';
      userEmail.value = '';
      confirmationUrl.value = '';
      successMessage.value = '';
    }, 3000);
    
  } catch (error: any) {
    errorMessage.value = error.data?.statusMessage || error.message || 'Failed to send email';
  } finally {
    isLoading.value = false;
  }
};
</script>
