<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="container mx-auto px-4 py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-full text-2xl mb-4">
          🛡️
        </div>
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Volnt Auth Email System</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
          Custom authentication emails with mobile-first design using Resend. 
          Simple, clean, and consistent with your existing email templates.
        </p>
      </div>

      <!-- Features Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <!-- Welcome Email -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-2xl mr-4">
              👋
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Welcome Emails</h3>
          </div>
          <p class="text-gray-600 mb-4">
            Automated welcome emails sent after user signup with mobile-optimized design.
          </p>
          <ul class="text-sm text-gray-500 space-y-1 mb-4">
            <li>✓ Mobile-first responsive design</li>
            <li>✓ Consistent with existing templates</li>
            <li>✓ Optional email confirmation</li>
            <li>✓ Automated integration</li>
          </ul>
          <NuxtLink 
            to="/auth/send-welcome-email" 
            class="btn btn-primary btn-sm w-full"
          >
            Send Welcome Email
          </NuxtLink>
        </div>

        <!-- Email Preview -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-2xl mr-4">
              👁️
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Email Preview</h3>
          </div>
          <p class="text-gray-600 mb-4">
            Preview how your auth emails look on mobile and desktop devices.
          </p>
          <ul class="text-sm text-gray-500 space-y-1 mb-4">
            <li>✓ Mobile & desktop views</li>
            <li>✓ Live customization</li>
            <li>✓ Test email sending</li>
            <li>✓ Code examples</li>
          </ul>
          <NuxtLink 
            to="/auth/email-preview" 
            class="btn btn-secondary btn-sm w-full"
          >
            Preview Templates
          </NuxtLink>
        </div>

        <!-- Registration Demo -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center text-2xl mr-4">
              🚀
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Registration Demo</h3>
          </div>
          <p class="text-gray-600 mb-4">
            See the complete signup flow with automated welcome email integration.
          </p>
          <ul class="text-sm text-gray-500 space-y-1 mb-4">
            <li>✓ Complete signup flow</li>
            <li>✓ Automated email sending</li>
            <li>✓ Error handling</li>
            <li>✓ Success feedback</li>
          </ul>
          <NuxtLink 
            to="/auth/register-with-email" 
            class="btn btn-accent btn-sm w-full"
          >
            Try Demo Signup
          </NuxtLink>
        </div>
      </div>

      <!-- Technical Details -->
      <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Technical Implementation</h2>
        
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Email Design Features</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Mobile-first responsive design (max-width: 480px)
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Consistent with existing Volnt email templates
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Clean, minimal content with clear CTAs
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Emoji-based visual hierarchy
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Optimized for email clients
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Integration Options</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Composable for easy Vue integration
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automatic user data extraction
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Conditional confirmation URLs
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Error handling and fallbacks
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Uses existing Resend infrastructure
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Quick Start -->
      <div class="bg-gray-900 text-white rounded-xl p-8">
        <h2 class="text-2xl font-bold mb-4">Quick Start</h2>
        <p class="text-gray-300 mb-6">
          Add welcome emails to your signup flow in just a few lines of code:
        </p>
        
        <div class="bg-gray-800 rounded-lg p-4 overflow-x-auto">
          <pre class="text-sm"><code class="text-green-400">// 1. Import the composable
const { handleNewUserSignup } = useAuthEmails();

// 2. After successful Supabase signup
const { data, error } = await supabase.auth.signUp({
  email: userEmail,
  password: userPassword,
  options: { data: { full_name: userName } }
});

// 3. Send welcome email automatically
if (data.user) {
  await handleNewUserSignup(data.user, {
    sendWelcomeEmail: true,
    includeConfirmationUrl: !data.user.email_confirmed_at
  });
}</code></pre>
        </div>
        
        <div class="mt-6 flex flex-wrap gap-4">
          <NuxtLink to="/auth/register-with-email" class="btn btn-primary">
            See Full Example
          </NuxtLink>
          <NuxtLink to="/auth/email-preview" class="btn btn-outline btn-white">
            Preview Emails
          </NuxtLink>
        </div>
      </div>

      <!-- Navigation -->
      <div class="text-center mt-12">
        <NuxtLink to="/" class="text-blue-600 hover:text-blue-800 font-medium">
          ← Back to Home
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'default'
});
</script>
