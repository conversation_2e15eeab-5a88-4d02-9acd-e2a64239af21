<template>
  <div class="text-center">
    <div class="flex justify-center">
      <div class="animate-pulse-slow bg-primary/10 p-4 rounded-full mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
    </div>
    <h1 class="text-2xl font-bold mb-2">{{ headingText }}</h1>
    <p class="mb-6">{{ messageText }}</p>
    <NuxtLink :to="nextPath" class="btn btn-primary">
      {{ buttonText }}
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'auth'
});

const route = useRoute();
const user = useSupabaseUser();

const headingText = computed(() => {
  if (route.query.error) return 'Authentication Error';
  if (user.value) return 'Welcome to Volnt!';
  return 'Confirming Your Account';
});

const messageText = computed(() => {
  if (route.query.error) return `There was an error: ${route.query.error}`;
  if (user.value) return 'You have successfully authenticated your Volnt account.';
  return 'Please wait while we confirm your account...';
});

const buttonText = computed(() => {
  if (route.query.error) return 'Try Again';
  if (user.value) return 'Go to Dashboard';
  return 'Continue';
});

const nextPath = computed(() => {
  if (route.query.error) return '/auth/login';
  if (user.value) return '/dashboard';
  return '/auth/login';
});

// Auto-redirect if user is authenticated
watchEffect(() => {
  if (user.value) {
    setTimeout(() => {
      navigateTo('/dashboard');
    }, 2000);
  }
});
</script>