<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Create Your Volnt Account</h1>
    
    <form @submit.prevent="handleRegister" class="space-y-4">
      <div v-if="errorMessage" class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>{{ errorMessage }}</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">First Name</span>
          </label>
          <input 
            type="text" 
            v-model="firstName"
            class="input input-bordered w-full" 
            placeholder="John"
            required
          />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">Last Name</span>
          </label>
          <input 
            type="text" 
            v-model="lastName"
            class="input input-bordered w-full" 
            placeholder="Doe"
            required
          />
        </div>
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Email</span>
        </label>
        <input 
          type="email" 
          v-model="email"
          class="input input-bordered w-full" 
          placeholder="<EMAIL>"
          required
        />
      </div>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">Password</span>
        </label>
        <div class="relative">
          <input 
            :type="showPassword ? 'text' : 'password'" 
            v-model="password"
            class="input input-bordered w-full pr-10" 
            placeholder="••••••••"
            required
          />
          <button 
            type="button"
            @click="showPassword = !showPassword"
            class="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7A9.97 9.97 0 014.02 8.971m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        </div>
        <div class="mt-2">
          <ul class="text-xs space-y-1">
            <li class="flex items-center" :class="hasMinLength ? 'text-success' : 'text-base-content/50'">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" :class="hasMinLength ? 'text-success' : 'text-base-content/50'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              At least 8 characters
            </li>
            <li class="flex items-center" :class="hasUpperCase ? 'text-success' : 'text-base-content/50'">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" :class="hasUpperCase ? 'text-success' : 'text-base-content/50'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Contains uppercase letter
            </li>
            <li class="flex items-center" :class="hasNumber ? 'text-success' : 'text-base-content/50'">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" :class="hasNumber ? 'text-success' : 'text-base-content/50'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Contains number
            </li>
          </ul>
        </div>
      </div>
      
      <div class="form-control">
        <label class="label cursor-pointer">
          <span class="label-text">I agree to the <NuxtLink to="/terms">Terms of Service</NuxtLink> and <NuxtLink to="/privacy" class="link link-primary">Privacy Policy</NuxtLink></span> 
          <input type="checkbox" v-model="agreeToTerms" class="checkbox checkbox-primary" required />
        </label>
      </div>
      
      <button 
        type="submit" 
        class="btn btn-primary w-full" 
        :class="{ 'loading': isLoading }"
        :disabled="isLoading || !isPasswordValid || !agreeToTerms"
      >
        {{ isLoading ? 'Creating account...' : 'Create Account' }}
      </button>
      
      <p class="text-center mt-4 text-sm">
        Already have an account? 
        <NuxtLink to="/auth/login" class="text-primary font-medium">
          Login
        </NuxtLink>
      </p>
    </form>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod';

definePageMeta({
  layout: 'auth'
});

const supabase = useSupabaseClient();
const router = useRouter();

const firstName = ref('');
const lastName = ref('');
const email = ref('');
const password = ref('');
const showPassword = ref(false);
const agreeToTerms = ref(false);
const isLoading = ref(false);
const errorMessage = ref('');

// Password validation
const hasMinLength = computed(() => password.value.length >= 8);
const hasUpperCase = computed(() => /[A-Z]/.test(password.value));
const hasNumber = computed(() => /[0-9]/.test(password.value));
const isPasswordValid = computed(() => 
  hasMinLength.value && hasUpperCase.value && hasNumber.value
);

// Validation schema using Zod
const userSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain an uppercase letter')
    .regex(/[0-9]/, 'Password must contain a number'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  agreeToTerms: z.literal(true, {
    errorMap: () => ({ message: 'You must agree to the terms' }),
  })
});

const handleRegister = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  
  try {
    // Validate inputs
    const validatedData = userSchema.parse({
      email: email.value,
      password: password.value,
      firstName: firstName.value,
      lastName: lastName.value,
      agreeToTerms: agreeToTerms.value
    });
    
    // Register with Supabase
    const { error } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        data: {
          first_name: validatedData.firstName,
          last_name: validatedData.lastName,
          full_name: `${validatedData.firstName} ${validatedData.lastName}`
        }
      }
    });
    
    if (error) throw error;
    
    // Redirect to dashboard
    router.push('/dashboard');
    
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      errorMessage.value = error.errors[0].message;
    } else {
      errorMessage.value = error.message || 'Failed to create account';
    }
  } finally {
    isLoading.value = false;
  }
};
</script>