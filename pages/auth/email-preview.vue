<template>
  <div class="min-h-screen bg-gray-100 py-8">
    <div class="container mx-auto px-4">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Volnt Auth Email Templates</h1>
        <p class="text-gray-600">Mobile-first, simple and clean design</p>
      </div>

      <!-- Controls -->
      <div class="flex justify-center mb-8">
        <div class="bg-white rounded-lg shadow-md p-4 flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="viewMode" 
              value="mobile" 
              class="radio radio-primary mr-2"
            />
            <span>Mobile View</span>
          </label>
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="viewMode" 
              value="desktop" 
              class="radio radio-primary mr-2"
            />
            <span>Desktop View</span>
          </label>
        </div>
      </div>

      <!-- Email Preview -->
      <div class="flex justify-center">
        <div 
          class="bg-white rounded-lg shadow-xl overflow-hidden transition-all duration-300"
          :class="viewMode === 'mobile' ? 'w-full max-w-sm' : 'w-full max-w-2xl'"
        >
          <!-- Email Header -->
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 text-center">
            <div class="text-4xl mb-3">🛡️</div>
            <h2 class="text-xl font-semibold">Welcome to Volnt</h2>
          </div>

          <!-- Email Content -->
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Hi {{ userName }}!</h3>
            
            <!-- Welcome Message -->
            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
              <p class="text-blue-800">
                <strong>Your digital legacy is now protected.</strong> 
                You've taken an important step in securing your digital assets for the future.
              </p>
            </div>

            <!-- Features List -->
            <div class="space-y-3 mb-6">
              <div class="flex items-center text-gray-700">
                <span class="text-xl mr-3">📝</span>
                <span>Create your digital will</span>
              </div>
              <div class="flex items-center text-gray-700">
                <span class="text-xl mr-3">🔒</span>
                <span>Secure asset management</span>
              </div>
              <div class="flex items-center text-gray-700">
                <span class="text-xl mr-3">👥</span>
                <span>Add trusted contacts</span>
              </div>
              <div class="flex items-center text-gray-700">
                <span class="text-xl mr-3">⚡</span>
                <span>Automated triggers</span>
              </div>
            </div>

            <!-- CTA Button -->
            <div class="text-center mb-6">
              <button 
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                :class="viewMode === 'desktop' ? 'max-w-xs' : ''"
              >
                {{ showConfirmation ? 'Confirm Your Email' : 'Get Started' }}
              </button>
            </div>

            <!-- Conditional Message -->
            <div v-if="showConfirmation" class="text-center text-sm text-gray-600 mb-4">
              Please confirm your email to get started
            </div>
          </div>

          <!-- Email Footer -->
          <div class="bg-gray-50 px-6 py-4 text-center text-sm text-gray-600">
            <p class="font-medium text-gray-900 mb-1">Volnt</p>
            <p>Secure your digital legacy</p>
            <p class="mt-2">Questions? Reply to this email or visit our help center.</p>
          </div>
        </div>
      </div>

      <!-- Controls Panel -->
      <div class="mt-8 flex justify-center">
        <div class="bg-white rounded-lg shadow-md p-6 w-full max-w-md">
          <h3 class="text-lg font-medium mb-4">Email Customization</h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                User Name
              </label>
              <input 
                type="text" 
                v-model="userName" 
                class="input input-bordered w-full"
                placeholder="Enter user name"
              />
            </div>
            
            <div class="flex items-center">
              <input 
                type="checkbox" 
                v-model="showConfirmation" 
                class="checkbox checkbox-primary mr-2"
              />
              <label class="text-sm text-gray-700">
                Show email confirmation
              </label>
            </div>
          </div>

          <!-- Test Email Button -->
          <div class="mt-6">
            <button 
              @click="sendTestEmail" 
              class="btn btn-primary w-full"
              :class="{ 'loading': isSending }"
              :disabled="isSending"
            >
              {{ isSending ? 'Sending...' : 'Send Test Email' }}
            </button>
          </div>

          <div v-if="testResult" class="mt-4">
            <div 
              class="alert"
              :class="testResult.success ? 'alert-success' : 'alert-error'"
            >
              <span>{{ testResult.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="mt-12 max-w-4xl mx-auto">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Integration Example</h3>
        <div class="bg-gray-900 text-gray-100 p-6 rounded-lg overflow-x-auto">
          <pre><code>// In your signup handler
const { handleNewUserSignup } = useAuthEmails();

// After successful Supabase signup
const emailResult = await handleNewUserSignup(data.user, {
  sendWelcomeEmail: true,
  includeConfirmationUrl: !data.user.email_confirmed_at
});

// Or send manually
const { sendWelcomeAfterSignup } = useAuthEmails();
await sendWelcomeAfterSignup(
  '<EMAIL>', 
  'John Doe',
  { includeConfirmationUrl: true }
);</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'default'
});

const { sendWelcomeAfterSignup } = useAuthEmails();

const viewMode = ref('mobile');
const userName = ref('John Doe');
const showConfirmation = ref(true);
const isSending = ref(false);
const testResult = ref<{ success: boolean; message: string } | null>(null);

const sendTestEmail = async () => {
  isSending.value = true;
  testResult.value = null;
  
  try {
    // You would replace this with a real email address for testing
    const testEmail = '<EMAIL>';
    
    const result = await sendWelcomeAfterSignup(
      testEmail,
      userName.value,
      { includeConfirmationUrl: showConfirmation.value }
    );
    
    testResult.value = {
      success: result.success,
      message: result.success 
        ? `Test email sent to ${testEmail}` 
        : 'Failed to send test email'
    };
  } catch (error) {
    testResult.value = {
      success: false,
      message: 'Error sending test email'
    };
  } finally {
    isSending.value = false;
  }
};
</script>
