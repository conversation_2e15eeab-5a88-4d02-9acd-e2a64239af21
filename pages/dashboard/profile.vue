<template>
  <div>
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-2 sm:gap-0">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold">Profile Settings</h1>
        <p class="text-base-content/70 text-sm sm:text-base">Manage your personal information and account settings</p>
      </div>
      <div class="flex flex-row gap-2 mt-2 sm:mt-0">
        <button @click="handleLogout" class="btn btn-xs sm:btn-sm btn-outline btn-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          Logout
        </button>
      </div>
    </div>

    <!-- Profile Information Card -->
    <div class="bg-base-100 shadow-md rounded-box p-4 sm:p-6 mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
        <div class="avatar">
          <div class="w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-bold">
            {{ userInitials }}
          </div>
        </div>
        <div class="flex-1">
          <h2 class="text-xl font-semibold">{{ displayName }}</h2>
          <p class="text-base-content">{{ user?.email }}</p>
          <div class="flex items-center mt-2">
            <span class="badge" :class="userStore.isProfileComplete ? 'badge-success' : 'badge-warning'">
              {{ userStore.isProfileComplete ? 'Profile Complete' : 'Profile Incomplete' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Quick Name Edit Form -->
      <div class="divider">Personal Information</div>
      
      <form @submit.prevent="updateProfile" class="space-y-4">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">First Name</span>
            </label>
            <input 
              type="text" 
              v-model="profileForm.firstName"
              class="input input-bordered w-full" 
              placeholder="Enter your first name"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Last Name</span>
            </label>
            <input 
              type="text" 
              v-model="profileForm.lastName"
              class="input input-bordered w-full" 
              placeholder="Enter your last name"
              required
            />
          </div>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">Phone Number (Optional)</span>
          </label>
          <input 
            type="tel" 
            v-model="profileForm.phoneNumber"
            class="input input-bordered w-full" 
            placeholder="Enter your phone number"
          />
        </div>

        <div class="flex flex-col sm:flex-row gap-2">
          <button 
            type="submit" 
            class="btn btn-primary"
            :class="{ 'loading': userStore.isLoading }"
            :disabled="userStore.isLoading || !hasChanges"
          >
            {{ userStore.isLoading ? 'Updating...' : 'Update Profile' }}
          </button>
          
          <button 
            type="button" 
            @click="resetForm"
            class="btn btn-outline"
            :disabled="userStore.isLoading || !hasChanges"
          >
            Reset
          </button>
        </div>

        <div v-if="updateMessage" class="alert" :class="updateMessage.type === 'success' ? 'alert-success' : 'alert-error'">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path v-if="updateMessage.type === 'success'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ updateMessage.text }}</span>
        </div>
      </form>
    </div>

    <!-- Will Overview Card -->
    <div class="bg-base-100 shadow-md rounded-box p-4 sm:p-6 mb-6">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4">
        <h3 class="text-lg font-semibold">Will Overview</h3>
        <NuxtLink to="/dashboard/will" class="btn btn-sm btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Will
        </NuxtLink>
      </div>

      <div v-if="willStore.willDocument" class="space-y-4">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div class="stat bg-base-200 rounded-box">
            <div class="stat-title text-xs">Status</div>
            <div class="stat-value text-sm" :class="willStore.isPublished ? 'text-success' : 'text-warning'">
              {{ willStore.isPublished ? 'Published' : 'Draft' }}
            </div>
          </div>
          
          <div class="stat bg-base-200 rounded-box">
            <div class="stat-title text-xs">Word Count</div>
            <div class="stat-value text-sm">{{ willStore.wordCount }}</div>
          </div>
          
          <div class="stat bg-base-200 rounded-box">
            <div class="stat-title text-xs">Last Updated</div>
            <div class="stat-value text-xs">{{ formatDate(willStore.willDocument.lastEdited) }}</div>
          </div>
        </div>

        <div class="divider">Will Preview</div>
        
        <div class="bg-base-200 rounded-box p-4 max-h-64 overflow-y-auto">
          <h4 class="font-semibold mb-2">{{ willStore.willDocument.title }}</h4>
          <div 
            class="prose prose-sm max-w-none text-base-content/80"
            v-html="willPreview"
          ></div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-base-content/30 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-base-content/70 mb-4">No will document found</p>
        <NuxtLink to="/dashboard/will" class="btn btn-primary">Create Your Will</NuxtLink>
      </div>
    </div>

    <!-- Account Information -->
    <div class="bg-base-100 shadow-md rounded-box p-4 sm:p-6">
      <h3 class="text-lg font-semibold mb-4">Account Information</h3>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="label">
            <span class="label-text">Email Address</span>
          </label>
          <input 
            type="email" 
            :value="user?.email"
            class="input input-bordered w-full" 
            disabled
          />
          <label class="label">
            <span class="label-text-alt">Email cannot be changed</span>
          </label>
        </div>
        
        <div>
          <label class="label">
            <span class="label-text">Account Created</span>
          </label>
          <input 
            type="text" 
            :value="formatDate(user?.created_at)"
            class="input input-bordered w-full" 
            disabled
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useUserStore } from '~/stores/userStore';
import { useWillStore } from '~/stores/willStore';

definePageMeta({
  layout: 'dashboard'
});

const user = useSupabaseUser();
const supabase = useSupabaseClient();
const router = useRouter();
const userStore = useUserStore();
const willStore = useWillStore();

// Form state
const profileForm = ref({
  firstName: '',
  lastName: '',
  phoneNumber: ''
});

const updateMessage = ref<{ type: 'success' | 'error', text: string } | null>(null);

// Computed properties
const userInitials = computed(() => {
  if (userStore.profile?.firstName && userStore.profile?.lastName) {
    return `${userStore.profile.firstName[0]}${userStore.profile.lastName[0]}`.toUpperCase();
  }
  if (user.value?.email) {
    return user.value.email.substring(0, 2).toUpperCase();
  }
  return '??';
});

const displayName = computed(() => {
  if (userStore.fullName) {
    return userStore.fullName;
  }
  if (user.value?.email) {
    return user.value.email.split('@')[0];
  }
  return 'User';
});

const hasChanges = computed(() => {
  if (!userStore.profile) return false;
  return (
    profileForm.value.firstName !== userStore.profile.firstName ||
    profileForm.value.lastName !== userStore.profile.lastName ||
    profileForm.value.phoneNumber !== (userStore.profile.phoneNumber || '')
  );
});

import DOMPurify from 'dompurify';

const willPreview = computed(() => {
  if (!willStore.willDocument?.content) return '';
  // Truncate content for preview
  const raw = willStore.willDocument.content;
   const maxLength = 300;
  const truncated = raw.length > maxLength ? raw.substring(0, maxLength) + '…' : raw;
  // Sanitize to avoid XSS
  return DOMPurify.sanitize(truncated);

  return truncated;
});

// Methods
const resetForm = () => {
  if (userStore.profile) {
    profileForm.value = {
      firstName: userStore.profile.firstName,
      lastName: userStore.profile.lastName,
      phoneNumber: userStore.profile.phoneNumber || ''
    };
  }
  updateMessage.value = null;
};

const updateProfile = async () => {
  updateMessage.value = null;
  let result;
  try {
    result = await userStore.updateProfile({
     firstName: profileForm.value.firstName,
     lastName: profileForm.value.lastName,
     phoneNumber: profileForm.value.phoneNumber || undefined
    });
  } catch (err) {
    console.error(err);
    updateMessage.value = { type: 'error', text: 'Unexpected error updating profile' };
    return;
  }
  
  if (result.success) {
    updateMessage.value = {
      type: 'success',
      text: 'Profile updated successfully!'
    };
    setTimeout(() => {
      updateMessage.value = null;
    }, 3000);
  } else {
    updateMessage.value = {
      type: 'error',
      text: result.error || 'Failed to update profile'
    };
  }
};

const handleLogout = async () => {
  try {
    await supabase.auth.signOut();
    await userStore.clearUserData();
    router.push('/');
  } catch (error) {
    console.error('Error logging out:', error);
  }
};

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'Unknown';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Lifecycle
onMounted(async () => {
  await Promise.all([
    userStore.fetchProfile(),
    willStore.fetchWillDocument()
  ]);
  resetForm();
});

// Watch for profile changes
watch(() => userStore.profile, () => {
  resetForm();
}, { deep: true });
</script>
