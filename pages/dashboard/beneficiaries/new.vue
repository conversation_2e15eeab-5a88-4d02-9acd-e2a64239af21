<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Add New Beneficiary</h1>
        <p class="text-base-content/70">Add someone who will inherit your assets</p>
      </div>
      
      <NuxtLink to="/dashboard/beneficiaries" class="btn btn-sm btn-ghost">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Beneficiaries
      </NuxtLink>
    </div>
    
    <div class="bg-base-100 shadow-md rounded-lg p-6">
      <form @submit.prevent="saveBeneficiary" class="space-y-6">
        <!-- Personal Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Full Name *</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.name"
                class="input input-bordered w-full" 
                placeholder="John Doe"
                required
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email Address *</span>
              </label>
              <input 
                type="email" 
                v-model="beneficiaryData.email"
                class="input input-bordered w-full" 
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Phone Number</span>
              </label>
              <input 
                type="tel" 
                v-model="beneficiaryData.phoneNumber"
                class="input input-bordered w-full" 
                placeholder="+****************"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Relationship</span>
              </label>
              <select v-model="beneficiaryData.relationship" class="select select-bordered w-full">
                <option value="">Select relationship</option>
                <option value="Spouse">Spouse</option>
                <option value="Child">Child</option>
                <option value="Parent">Parent</option>
                <option value="Sibling">Sibling</option>
                <option value="Friend">Friend</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Digital Executor Section -->
        <div class="card bg-amber-50 border border-amber-200 p-4">
          <div class="flex items-center mb-4">
            <Icon name="mdi:shield-crown" class="h-5 w-5 text-amber-600 mr-2" />
            <h3 class="font-semibold text-lg text-amber-800">Digital Executor</h3>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="beneficiaryData.isDigitalExecutor" 
                  class="checkbox checkbox-warning mr-3"
                />
                <div>
                  <span class="label-text font-medium">Designate as Digital Executor</span>
                  <div class="text-sm text-base-content/70">
                    This person will have the authority to manage and distribute your digital assets
                  </div>
                </div>
              </div>
            </label>
          </div>
          
          <div v-if="beneficiaryData.isDigitalExecutor" class="alert alert-warning mt-4">
            <Icon name="mdi:information" class="h-5 w-5" />
            <div>
              <h4 class="font-bold">Digital Executor Responsibilities</h4>
              <div class="text-sm mt-1">
                <ul class="list-disc list-inside space-y-1">
                  <li>Access and manage all digital accounts and assets</li>
                  <li>Distribute digital assets according to your will</li>
                  <li>Handle password managers and encrypted data</li>
                  <li>Coordinate with other executors and beneficiaries</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div v-if="currentDigitalExecutor && beneficiaryData.isDigitalExecutor" class="alert alert-info mt-4">
            <Icon name="mdi:information" class="h-5 w-5" />
            <div>
              <h4 class="font-bold">Current Digital Executor</h4>
              <div class="text-sm">
                <strong>{{ currentDigitalExecutor.name }}</strong> is currently your Digital Executor. 
                Saving this beneficiary as Digital Executor will remove that designation from {{ currentDigitalExecutor.name }}.
              </div>
            </div>
          </div>
        </div>
        
        <!-- Access & Permissions -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Access & Permissions</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Access Level</span>
              </label>
              <select v-model="beneficiaryData.accessLevel" class="select select-bordered w-full">
                <option value="LIMITED">Limited - Basic information only</option>
                <option value="READ_ONLY">Read Only - Can view but not modify</option>
                <option value="FULL">Full Access - Can view and manage</option>
              </select>
              <label class="label">
                <span class="label-text-alt">Determines what this beneficiary can access</span>
              </label>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Notification Preference</span>
              </label>
              <select v-model="beneficiaryData.notificationPreference" class="select select-bordered w-full">
                <option value="EMAIL">Email notifications</option>
                <option value="SMS">SMS notifications</option>
                <option value="BOTH">Both email and SMS</option>
                <option value="NONE">No notifications</option>
              </select>
              <label class="label">
                <span class="label-text-alt">How they'll be notified about updates</span>
              </label>
            </div>
          </div>
          
          <div class="alert alert-info mt-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 class="font-bold">Access Level Information</h3>
              <div class="text-sm mt-1">
                <p><strong>Limited:</strong> Can only see basic asset information</p>
                <p><strong>Read Only:</strong> Can view detailed asset information but cannot make changes</p>
                <p><strong>Full Access:</strong> Can view and manage assets according to your will</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Form Actions -->
        <div class="flex justify-end space-x-4">
          <NuxtLink to="/dashboard/beneficiaries" class="btn btn-ghost">
            Cancel
          </NuxtLink>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :class="{ 'loading': beneficiaryStore.isLoading }"
            :disabled="beneficiaryStore.isLoading || !beneficiaryData.name || !beneficiaryData.email"
          >
            {{ beneficiaryStore.isLoading ? 'Saving...' : 'Save Beneficiary' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AccessLevel, NotificationPreference } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const beneficiaryStore = useBeneficiaryStore();

// Form data
const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference,
  isDigitalExecutor: false
});

const currentDigitalExecutor = computed(() => {
  return beneficiaryStore.digitalExecutor;
});

const saveBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email) return;
  
  const beneficiary = await beneficiaryStore.createBeneficiary({
    name: beneficiaryData.value.name,
    email: beneficiaryData.value.email,
    phoneNumber: beneficiaryData.value.phoneNumber || undefined,
    relationship: beneficiaryData.value.relationship || undefined,
    accessLevel: beneficiaryData.value.accessLevel,
    notificationPreference: beneficiaryData.value.notificationPreference,
    isDigitalExecutor: beneficiaryData.value.isDigitalExecutor
  });
  
  if (beneficiary) {
    navigateTo('/dashboard/beneficiaries');
  }
};

// Fetch current beneficiaries on mount to check for existing digital executor
onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries();
});
</script>