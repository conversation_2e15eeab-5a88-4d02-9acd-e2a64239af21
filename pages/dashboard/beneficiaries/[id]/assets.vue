<template>
  <div>
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Manage Assets</h1>
        <p class="text-base-content/70">{{ beneficiary?.name || 'Beneficiary' }}</p>
      </div>

      <NuxtLink :to="`/dashboard/beneficiaries`" class="btn btn-sm btn-ghost">
        <Icon name="mdi:arrow-left" class="h-4 w-4 mr-1" />
        Back to Beneficiaries
      </NuxtLink>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Beneficiary Not Found -->
    <div v-else-if="!beneficiary" class="bg-base-100 rounded-lg p-8 text-center">
      <div class="mb-4 flex justify-center">
        <div class="rounded-full bg-error/10 p-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      </div>
      <h3 class="text-xl font-medium mb-2">Beneficiary Not Found</h3>
      <p class="text-base-content/70 mb-6">The beneficiary you're looking for doesn't exist or has been deleted.</p>
      <NuxtLink to="/dashboard/beneficiaries" class="btn btn-primary">Back to Beneficiaries</NuxtLink>
    </div>

    <!-- Main Content -->
    <div v-else class="space-y-6">
      <!-- Beneficiary Info Card -->
      <div class="card bg-base-100 shadow-md">
        <div class="card-body">
          <div class="flex items-center gap-4">
            <div class="avatar">
              <div class="w-16 h-16 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-medium">
                {{ getInitials(beneficiary.name) }}
              </div>
            </div>
            <div class="flex-1">
              <h2 class="text-xl font-semibold">{{ beneficiary.name }}</h2>
              <p class="text-base-content/70">{{ beneficiary.relationship || 'Beneficiary' }}</p>
              <div class="flex items-center gap-4 mt-2">
                <span class="badge" :class="getAccessLevelBadgeClass(beneficiary.accessLevel)">
                  {{ formatAccessLevel(beneficiary.accessLevel) }}
                </span>
                <span class="text-sm text-base-content/70">{{ beneficiary.email }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Asset Assignments -->
      <div class="card bg-base-100 shadow-md">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Asset Assignments</h3>
            <button @click="showAddModal = true" class="btn btn-sm btn-primary">
              <Icon name="mdi:plus" class="h-4 w-4 mr-1" />
              Assign Asset
            </button>
          </div>

          <!-- Assignments List -->
          <div v-if="assignedAssets.length > 0" class="space-y-3">
            <div
              v-for="assignment in assignedAssets"
              :key="assignment.assetId"
              class="flex items-center justify-between p-4 border border-base-300 rounded-lg"
            >
              <div class="flex items-center">
                <div :class="`avatar bg-${getAssetTypeColor(assignment.assetId)}-100 p-2 rounded-full mr-4`">
                  <div class="w-10 h-10 text-base-content flex items-center justify-center">
                    <Icon :name="getAssetTypeIcon(assignment.assetId)" class="w-5 h-5" />
                  </div>
                </div>
                <div>
                  <div class="font-semibold">{{ getAssetName(assignment.assetId) }}</div>
                  <div class="text-sm text-base-content/70">{{ getAssetTypeLabel(assignment.assetId) }}</div>
                  <div v-if="getAssetValue(assignment.assetId)" class="badge badge-sm badge-outline mt-1">
                    {{ formatCurrency(getAssetValue(assignment.assetId), getAssetCurrency(assignment.assetId)) }}
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                  <input
                    type="number"
                    v-model.number="assignment.percentage"
                    @input="validateAndSavePercentage(assignment)"
                    min="1"
                    max="100"
                    class="input input-sm input-bordered w-20 text-center"
                  />
                  <span class="text-sm font-medium">%</span>
                </div>

                <div class="dropdown dropdown-end">
                  <label tabindex="0" class="btn btn-sm btn-ghost btn-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" />
                    </svg>
                  </label>
                  <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-48">
                    <li><a @click="viewAssetDetails(assignment.assetId)">View Asset Details</a></li>
                    <li><a @click="manageAssetBeneficiaries(assignment.assetId)">Manage All Beneficiaries</a></li>
                    <li><a @click="confirmRemoveAssignment(assignment.assetId)" class="text-error">Remove Assignment</a></li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Summary -->
            <div class="mt-4 p-4 bg-base-200 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-2xl font-bold text-primary">{{ assignedAssets.length }}</div>
                  <div class="text-sm text-base-content/70">Assets Assigned</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-success">{{ totalInheritanceValue }}</div>
                  <div class="text-sm text-base-content/70">Total Inheritance Value</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-info">{{ averagePercentage }}%</div>
                  <div class="text-sm text-base-content/70">Average Allocation</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <div class="mb-4 flex justify-center">
              <div class="rounded-full bg-base-200 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-base-content/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
                </svg>
              </div>
            </div>
            <h4 class="text-lg font-medium mb-2">No Assets Assigned</h4>
            <p class="text-base-content/70 mb-4">This beneficiary hasn't been assigned to any assets yet.</p>
            <button @click="showAddModal = true" class="btn btn-primary">
              Assign First Asset
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Asset Assignment Modal -->
    <div class="modal" :class="{ 'modal-open': showAddModal }">
      <div class="modal-box max-w-2xl">
        <div class="flex justify-between items-center mb-6">
          <h3 class="font-bold text-xl">Assign Assets</h3>
          <button @click="showAddModal = false" class="btn btn-sm btn-circle btn-ghost">
            <Icon name="mdi:close" class="h-5 w-5" />
          </button>
        </div>

        <div class="space-y-3 max-h-96 overflow-y-auto">
          <div
            v-for="asset in availableAssets"
            :key="asset.id"
            class="flex items-center justify-between p-3 border border-base-300 rounded-lg hover:bg-base-50 transition-colors"
          >
            <div class="flex items-center">
              <div :class="`avatar bg-${getAssetTypeColorDirect(asset.type)}-100 p-2 rounded-full mr-3`">
                <div class="w-8 h-8 text-base-content flex items-center justify-center">
                  <Icon :name="getAssetTypeIconDirect(asset.type)" class="w-4 h-4" />
                </div>
              </div>
              <div>
                <div class="font-medium">{{ asset.name }}</div>
                <div class="text-xs text-base-content/70">{{ getAssetTypeLabelDirect(asset.type) }}</div>
                <div v-if="asset.value" class="badge badge-xs badge-outline mt-1">
                  {{ formatCurrency(asset.value, asset.currency || 'USD') }}
                </div>
              </div>
            </div>
            <button
              @click="assignAsset(asset.id)"
              class="btn btn-sm btn-primary"
            >
              Assign
            </button>
          </div>
        </div>

        <div v-if="availableAssets.length === 0" class="text-center py-6 text-base-content/70">
          <p>All assets have been assigned to this beneficiary.</p>
          <NuxtLink to="/dashboard/assets/new" class="btn btn-sm btn-outline mt-2">
            Add New Asset
          </NuxtLink>
        </div>

        <div class="modal-action">
          <button @click="showAddModal = false" class="btn">Close</button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showAddModal = false"></div>
    </div>

    <!-- Remove Assignment Confirmation Modal -->
    <div class="modal" :class="{ 'modal-open': showRemoveModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Remove Assignment</h3>
        <p>Are you sure you want to remove this asset assignment? This action cannot be undone.</p>
        <div class="modal-action">
          <button @click="showRemoveModal = false" class="btn">Cancel</button>
          <button @click="removeAssignment" class="btn btn-error" :class="{ 'loading': beneficiaryStore.isLoading }">
            Remove
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showRemoveModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { Asset, AssetType, AccessLevel } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const route = useRoute();
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const isLoading = ref(true);
const showAddModal = ref(false);
const showRemoveModal = ref(false);
const assetToRemove = ref<string | null>(null);

const beneficiaryId = route.params.id as string;

// Computed properties
const beneficiary = computed(() => beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId));

const assignedAssets = computed(() => {
  return (beneficiary.value?.assets || []).map(a => ({
    assetId: a.assetId,
    percentage: a.percentage
  }));
});

const availableAssets = computed(() => {
  const assignedAssetIds = assignedAssets.value.map(a => a.assetId);
  return assetStore.assets.filter(asset => !assignedAssetIds.includes(asset.id));
});

const totalInheritanceValue = computed(() => {
  const total = assignedAssets.value.reduce((sum, assignment) => {
    const asset = assetStore.assets.find(a => a.id === assignment.assetId);
    if (asset?.value) {
      return sum + (asset.value * assignment.percentage / 100);
    }
    return sum;
  }, 0);

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  }).format(total);
});

const averagePercentage = computed(() => {
  if (assignedAssets.value.length === 0) return 0;
  const total = assignedAssets.value.reduce((sum, a) => sum + a.percentage, 0);
  return Math.round(total / assignedAssets.value.length);
});

// Helper functions
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const formatAccessLevel = (level: AccessLevel) => {
  const formatMap: Record<AccessLevel, string> = {
    FULL: 'Full Access',
    READ_ONLY: 'Read Only',
    LIMITED: 'Limited'
  };

  return formatMap[level] || level;
};

const getAccessLevelBadgeClass = (level: AccessLevel) => {
  const classMap: Record<AccessLevel, string> = {
    FULL: 'badge-primary',
    READ_ONLY: 'badge-secondary',
    LIMITED: 'badge-accent'
  };

  return classMap[level] || '';
};

const getAssetName = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.name || 'Unknown Asset';
};

const getAssetTypeLabel = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  if (!asset) return 'Unknown';

  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };

  return typeMap[asset.type] || 'Asset';
};

const getAssetTypeLabelDirect = (type: AssetType) => {
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };

  return typeMap[type] || 'Asset';
};

const getAssetTypeColor = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  if (!asset) return 'blue';

  return getAssetTypeColorDirect(asset.type);
};

const getAssetTypeColorDirect = (type: AssetType) => {
  const colorMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'blue',
    CRYPTOCURRENCY: 'yellow',
    SOCIAL_MEDIA: 'purple',
    FINANCIAL_ACCOUNT: 'green',
    REAL_ESTATE: 'amber',
    VEHICLE: 'sky',
    COLLECTIBLE: 'pink',
    INSURANCE: 'emerald',
    INTELLECTUAL_PROPERTY: 'indigo',
    PERSONAL_BELONGING: 'orange',
    CUSTOM: 'gray'
  };

  return colorMap[type] || 'blue';
};

const getAssetTypeIcon = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  if (!asset) return '';

  return getAssetTypeIconDirect(asset.type);
};

const getAssetTypeIconDirect = (type: AssetType) => {
  const iconMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'mdi:monitor',
    CRYPTOCURRENCY: 'mdi:bitcoin',
    SOCIAL_MEDIA: 'mdi:account-group',
    FINANCIAL_ACCOUNT: 'mdi:credit-card',
    REAL_ESTATE: 'mdi:home',
    VEHICLE: 'mdi:car',
    COLLECTIBLE: 'mdi:treasure-chest',
    INSURANCE: 'mdi:shield-check',
    INTELLECTUAL_PROPERTY: 'mdi:lightbulb',
    PERSONAL_BELONGING: 'mdi:bag-personal',
    CUSTOM: 'mdi:tag'
  };

  return iconMap[type] || iconMap.CUSTOM;
};

const getAssetValue = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.value || 0;
};

const getAssetCurrency = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.currency || 'USD';
};

const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};

// Actions
const validateAndSavePercentage = async (assignment: { assetId: string; percentage: number }) => {
  if (assignment.percentage < 1) assignment.percentage = 1;
  if (assignment.percentage > 100) assignment.percentage = 100;

  // Save the updated percentage
  await beneficiaryStore.assignAsset(beneficiaryId, assignment.assetId, assignment.percentage);

  // Refresh data
  await beneficiaryStore.fetchBeneficiaries();
};

const assignAsset = async (assetId: string) => {
  await beneficiaryStore.assignAsset(beneficiaryId, assetId, 25); // Default 25%
  await beneficiaryStore.fetchBeneficiaries();
  showAddModal.value = false;
};

const viewAssetDetails = (assetId: string) => {
  navigateTo(`/dashboard/assets/${assetId}`);
};

const manageAssetBeneficiaries = (assetId: string) => {
  navigateTo(`/dashboard/assets/${assetId}/beneficiaries`);
};

const confirmRemoveAssignment = (assetId: string) => {
  assetToRemove.value = assetId;
  showRemoveModal.value = true;
};

const removeAssignment = async () => {
  if (assetToRemove.value) {
    await beneficiaryStore.removeAssetAssignment(beneficiaryId, assetToRemove.value);
    await beneficiaryStore.fetchBeneficiaries();
    showRemoveModal.value = false;
    assetToRemove.value = null;
  }
};

// Fetch data on mount
onMounted(async () => {
  try {
    await Promise.all([
      assetStore.fetchAssets(),
      beneficiaryStore.fetchBeneficiaries()
    ]);
  } finally {
    isLoading.value = false;
  }
});
</script>