<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Edit Beneficiary</h1>
        <p class="text-base-content/70">Update beneficiary information</p>
      </div>
      
      <NuxtLink to="/dashboard/beneficiaries" class="btn btn-sm btn-ghost">
        <Icon icon="mdi:arrow-left" class="h-4 w-4 mr-1" />
        Back to Beneficiaries
      </NuxtLink>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- Beneficiary Not Found -->
    <div v-else-if="!beneficiary" class="bg-base-100 rounded-lg p-8 text-center">
      <div class="mb-4 flex justify-center">
        <div class="rounded-full bg-error/10 p-3">
          <Icon icon="mdi:alert-triangle" class="h-12 w-12 text-error" />
        </div>
      </div>
      <h3 class="text-xl font-medium mb-2">Beneficiary Not Found</h3>
      <p class="text-base-content/70 mb-6">The beneficiary you're trying to edit doesn't exist or has been deleted.</p>
      <NuxtLink to="/dashboard/beneficiaries" class="btn btn-primary">Back to Beneficiaries</NuxtLink>
    </div>
    
    <!-- Edit Form -->
    <div v-else class="bg-base-100 shadow-md rounded-lg p-6">
      <form @submit.prevent="updateBeneficiary" class="space-y-6">
        <!-- Personal Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Full Name *</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.name"
                class="input input-bordered w-full" 
                placeholder="John Doe"
                required
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email Address *</span>
              </label>
              <input 
                type="email" 
                v-model="beneficiaryData.email"
                class="input input-bordered w-full" 
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Phone Number</span>
              </label>
              <input 
                type="tel" 
                v-model="beneficiaryData.phoneNumber"
                class="input input-bordered w-full" 
                placeholder="+****************"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Relationship</span>
              </label>
              <select v-model="beneficiaryData.relationship" class="select select-bordered w-full">
                <option value="">Select relationship</option>
                <option value="Spouse">Spouse</option>
                <option value="Child">Child</option>
                <option value="Parent">Parent</option>
                <option value="Sibling">Sibling</option>
                <option value="Friend">Friend</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Digital Executor Section -->
        <div class="card bg-amber-50 border border-amber-200 p-4">
          <div class="flex items-center mb-4">
            <Icon icon="mdi:shield-crown" class="h-5 w-5 text-amber-600 mr-2" />
            <h3 class="font-semibold text-lg text-amber-800">Digital Executor</h3>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="beneficiaryData.isDigitalExecutor" 
                  class="checkbox checkbox-warning mr-3"
                />
                <div>
                  <span class="label-text font-medium">Designate as Digital Executor</span>
                  <div class="text-sm text-base-content/70">
                    This person will have the authority to manage and distribute your digital assets
                  </div>
                </div>
              </div>
            </label>
          </div>
          
          <div v-if="beneficiaryData.isDigitalExecutor" class="alert alert-warning mt-4">
            <Icon icon="mdi:information" class="h-5 w-5" />
            <div>
              <h4 class="font-bold">Digital Executor Responsibilities</h4>
              <div class="text-sm mt-1">
                <ul class="list-disc list-inside space-y-1">
                  <li>Access and manage all digital accounts and assets</li>
                  <li>Distribute digital assets according to your will</li>
                  <li>Handle password managers and encrypted data</li>
                  <li>Coordinate with other executors and beneficiaries</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div v-if="currentDigitalExecutor && currentDigitalExecutor.id !== beneficiaryId && beneficiaryData.isDigitalExecutor" class="alert alert-info mt-4">
            <Icon icon="mdi:information" class="h-5 w-5" />
            <div>
              <h4 class="font-bold">Current Digital Executor</h4>
              <div class="text-sm">
                <strong>{{ currentDigitalExecutor.name }}</strong> is currently your Digital Executor. 
                Saving this change will remove that designation from {{ currentDigitalExecutor.name }}.
              </div>
            </div>
          </div>
        </div>
        
        <!-- Access & Permissions -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Access & Permissions</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Access Level</span>
              </label>
              <select v-model="beneficiaryData.accessLevel" class="select select-bordered w-full">
                <option value="LIMITED">Limited - Basic information only</option>
                <option value="READ_ONLY">Read Only - Can view but not modify</option>
                <option value="FULL">Full Access - Can view and manage</option>
              </select>
              <label class="label">
                <span class="label-text-alt">Determines what this beneficiary can access</span>
              </label>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Notification Preference</span>
              </label>
              <select v-model="beneficiaryData.notificationPreference" class="select select-bordered w-full">
                <option value="EMAIL">Email notifications</option>
                <option value="SMS">SMS notifications</option>
                <option value="BOTH">Both email and SMS</option>
                <option value="NONE">No notifications</option>
              </select>
              <label class="label">
                <span class="label-text-alt">How they'll be notified about updates</span>
              </label>
            </div>
          </div>
        </div>
        
        <!-- Asset Assignments -->
        <div class="card bg-base-50 p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-semibold text-lg">Asset Assignments</h3>
            <NuxtLink :to="`/dashboard/beneficiaries/${beneficiaryId}/assets`" class="btn btn-sm btn-primary">
              <Icon icon="mdi:pencil" class="h-4 w-4 mr-1" />
              Manage Assets
            </NuxtLink>
          </div>
          
          <div v-if="beneficiary.assets && beneficiary.assets.length > 0" class="space-y-2">
            <div v-for="asset in beneficiary.assets" :key="asset.assetId" class="flex justify-between items-center p-3 bg-base-200 rounded-lg">
              <div>
                <span class="font-medium">{{ getAssetName(asset.assetId) }}</span>
                <span class="text-sm text-base-content/70 ml-2">{{ asset.percentage }}%</span>
              </div>
              <div class="badge badge-outline">{{ getAssetType(asset.assetId) }}</div>
            </div>
          </div>
          
          <div v-else class="text-center py-4">
            <p class="text-base-content/70">No assets assigned yet</p>
          </div>
        </div>
        
        <!-- Form Actions -->
        <div class="flex justify-end space-x-4">
          <NuxtLink to="/dashboard/beneficiaries" class="btn btn-ghost">
            Cancel
          </NuxtLink>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :class="{ 'loading': isSaving }"
            :disabled="isSaving || !beneficiaryData.name || !beneficiaryData.email"
          >
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useAssetStore } from '~/stores/assetStore';
import type { Beneficiary, AccessLevel, NotificationPreference } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const route = useRoute();
const beneficiaryId = route.params.id as string;

const beneficiaryStore = useBeneficiaryStore();
const assetStore = useAssetStore();

const isLoading = ref(true);
const isSaving = ref(false);
const beneficiary = ref<Beneficiary | null>(null);

const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference,
  isDigitalExecutor: false
});

const currentDigitalExecutor = computed(() => {
  return beneficiaryStore.digitalExecutor;
});

const getAssetName = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.name || 'Unknown Asset';
};

const getAssetType = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  if (!asset) return 'Unknown';
  
  const typeMap: Record<string, string> = {
    'DIGITAL_ACCOUNT': 'Digital Account',
    'CRYPTOCURRENCY': 'Cryptocurrency',
    'SOCIAL_MEDIA': 'Social Media',
    'FINANCIAL_ACCOUNT': 'Financial Account',
    'REAL_ESTATE': 'Real Estate',
    'VEHICLE': 'Vehicle',
    'COLLECTIBLE': 'Collectible',
    'INSURANCE': 'Insurance',
    'INTELLECTUAL_PROPERTY': 'Intellectual Property',
    'PERSONAL_BELONGING': 'Personal Belonging',
    'CUSTOM': 'Custom'
  };
  
  return typeMap[asset.type] || 'Asset';
};

const updateBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email) return;
  
  isSaving.value = true;
  
  try {
    const updatedBeneficiary = await beneficiaryStore.updateBeneficiary(beneficiaryId, {
      name: beneficiaryData.value.name,
      email: beneficiaryData.value.email,
      phoneNumber: beneficiaryData.value.phoneNumber || undefined,
      relationship: beneficiaryData.value.relationship || undefined,
      accessLevel: beneficiaryData.value.accessLevel,
      notificationPreference: beneficiaryData.value.notificationPreference,
      isDigitalExecutor: beneficiaryData.value.isDigitalExecutor
    });
    
    if (updatedBeneficiary) {
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'toast toast-top toast-end';
      notification.innerHTML = `
        <div class="alert alert-success">
          <span>Beneficiary updated successfully!</span>
        </div>
      `;
      document.body.appendChild(notification);
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 3000);
      
      // Refresh data
      await loadBeneficiary();
    }
  } catch (error) {
    console.error('Error updating beneficiary:', error);
  } finally {
    isSaving.value = false;
  }
};

const loadBeneficiary = async () => {
  isLoading.value = true;
  
  try {
    await Promise.all([
      beneficiaryStore.fetchBeneficiaries(),
      assetStore.fetchAssets()
    ]);
    
    beneficiary.value = await beneficiaryStore.fetchBeneficiary(beneficiaryId);
    
    if (beneficiary.value) {
      beneficiaryData.value = {
        name: beneficiary.value.name,
        email: beneficiary.value.email,
        phoneNumber: beneficiary.value.phoneNumber || '',
        relationship: beneficiary.value.relationship || '',
        accessLevel: beneficiary.value.accessLevel,
        notificationPreference: beneficiary.value.notificationPreference,
        isDigitalExecutor: beneficiary.value.isDigitalExecutor
      };
    }
  } catch (error) {
    console.error('Error loading beneficiary:', error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(async () => {
  await loadBeneficiary();
});
</script>