<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Trigger Settings</h1>
        <p class="text-base-content/70">Configure how your will is executed</p>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <div v-else class="space-y-6">
      <!-- Execution Trigger Settings -->
      <div class="bg-base-100 shadow-md rounded-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-semibold">Execution Trigger</h2>
            <p class="text-base-content/70">Configure when your will should be executed</p>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text mr-2">Active</span> 
              <input 
                type="checkbox" 
                v-model="triggerSettings.isActive" 
                class="toggle toggle-primary"
                @change="markAsChanged"
              />
            </label>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Inactivity Period (Days)</span>
              <span class="label-text-alt text-warning">Recommended: 7+ days</span>
            </label>
            <div class="flex items-center gap-2">
              <button 
                class="btn btn-sm btn-outline" 
                @click="adjustInactivity(-1)"
                :disabled="triggerSettings.inactivityPeriod <= 1"
              >-</button>
              <input 
                type="number" 
                v-model.number="triggerSettings.inactivityPeriod"
                @input="markAsChanged"
                min="1" 
                max="365" 
                class="input input-bordered w-24 text-center" 
              />
              <button 
                class="btn btn-sm btn-outline" 
                @click="adjustInactivity(1)"
                :disabled="triggerSettings.inactivityPeriod >= 365"
              >+</button>
              <span class="text-sm">days</span>
            </div>
            <label class="label">
              <span class="label-text-alt">Will execution process begins after this period of inactivity</span>
            </label>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Reminder Frequency (Days)</span>
              <span class="label-text-alt text-warning">Recommended: 14+ days</span>
            </label>
            <div class="flex items-center gap-2">
              <button 
                class="btn btn-sm btn-outline" 
                @click="adjustReminder(-1)"
                :disabled="triggerSettings.reminderFrequency <= 1"
              >-</button>
              <input 
                type="number" 
                v-model.number="triggerSettings.reminderFrequency"
                @input="markAsChanged"
                min="1" 
                max="90" 
                class="input input-bordered w-24 text-center" 
              />
              <button 
                class="btn btn-sm btn-outline" 
                @click="adjustReminder(1)"
                :disabled="triggerSettings.reminderFrequency >= 90"
              >+</button>
              <span class="text-sm">days</span>
            </div>
            <label class="label">
              <span class="label-text-alt">How often you'll receive activity reminders</span>
            </label>
          </div>
        </div>
        
        <div class="form-control mt-4">
          <label class="label">
            <span class="label-text">Verification Method</span>
          </label>
          <select 
            v-model="triggerSettings.verificationMethod" 
            class="select select-bordered w-full max-w-xs"
            @change="markAsChanged"
          >
            <option value="EMAIL">Email</option>
            <option value="SMS">SMS</option>
            <option value="SECURITY_QUESTIONS">Security Questions</option>
            <option value="TRUSTED_CONTACTS">Trusted Contacts</option>
          </select>
          <label class="label">
            <span class="label-text-alt">How we'll verify your status during the inactivity period</span>
          </label>
        </div>
        
        <div class="alert alert-info mt-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <span class="font-medium">How it works: </span>
            If we don't detect any activity from you for {{ triggerSettings.inactivityPeriod }} days, we\'ll begin the verification process. If you don\'t respond, your will execution process will begin.
          </div>
        </div>

        <!-- Save Button -->
        <div class="mt-6" v-if="hasChanges">
          <button 
            @click="saveTriggerSettings" 
            class="btn btn-primary"
            :class="{ 'loading': isSaving }"
            :disabled="isSaving"
          >
            {{ isSaving ? 'Saving...' : 'Save Trigger Settings' }}
          </button>
        </div>
      </div>
      
      <!-- Trusted Contacts Section -->
      <div class="bg-base-100 shadow-md rounded-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-semibold">Trusted Contacts</h2>
            <p class="text-base-content/70">People who can verify your status during trigger events</p>
          </div>
          
          <button @click="showAddContactModal = true" class="btn btn-sm btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Contact
          </button>
        </div>
        
        <div v-if="trustedContacts.length > 0" class="overflow-x-auto">
          <table class="table w-full">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="contact in trustedContacts" :key="contact.id">
                <td>
                  <div class="flex items-center space-x-3">
                    <div class="avatar">
                      <div class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center font-medium">
                        {{ getInitials(contact.name) }}
                      </div>
                    </div>
                    <div>
                      <div class="font-medium">{{ contact.name }}</div>
                      <div class="text-xs text-base-content/60">{{ contact.relationship || 'Contact' }}</div>
                    </div>
                  </div>
                </td>
                <td>{{ contact.email }}</td>
                <td>
                  <span class="badge" :class="contact.isVerified ? 'badge-success' : 'badge-warning'">
                    {{ contact.isVerified ? 'Verified' : 'Pending' }}
                  </span>
                </td>
                <td>
                  <div class="flex space-x-1">
                    <button 
                      v-if="!contact.isVerified"
                      @click="resendInvitation(contact.id)" 
                      class="btn btn-xs btn-ghost tooltip" 
                      data-tip="Resend Invitation"
                      :disabled="isResending"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    </button>
                    <button 
                      @click="confirmDeleteContact(contact.id)" 
                      class="btn btn-xs btn-ghost tooltip" 
                      data-tip="Remove Contact"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-else class="text-center py-8">
          <div class="mb-4 flex justify-center">
            <div class="rounded-full bg-base-200 p-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-base-content/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <h4 class="text-lg font-medium mb-2">No Trusted Contacts</h4>
          <p class="text-base-content/70 mb-4">Add people who can verify your status during trigger events</p>
          <button @click="showAddContactModal = true" class="btn btn-primary">
            Add Your First Contact
          </button>
        </div>
      </div>
      
      <!-- Email Alert System -->
      <div class="bg-base-100 shadow-md rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Email Alert System</h2>
        <p class="text-base-content/70 mb-6">Configure automated email alerts for inactivity periods</p>
        
        <div class="space-y-4">
          <div v-for="(alert, index) in alerts" :key="index" class="card bg-base-50 p-4">
            <div class="flex justify-between items-start mb-4">
              <h3 class="font-medium">Alert {{ index + 1 }}</h3>
              <button 
                v-if="alerts.length > 1"
                @click="removeAlert(index)" 
                class="btn btn-xs btn-ghost text-error"
              >
                Remove
              </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Send alert after</span>
                </label>
                <div class="flex items-center gap-2">
                  <button class="btn btn-xs btn-outline" @click="decrementDays(alert)">-</button>
                  <input 
                    type="number" 
                    v-model.number="alert.days"
                    min="1" 
                    max="120" 
                    class="input input-sm w-20 text-center" 
                  />
                  <button class="btn btn-xs btn-outline" @click="incrementDays(alert)">+</button>
                  <span>days of inactivity</span>
                </div>
              </div>
            </div>
            
            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text">Recipients</span>
              </label>
              
              <!-- Beneficiaries -->
              <div v-if="hasBeneficiaries" class="mb-3">
                <h4 class="text-sm font-medium mb-2">Beneficiaries</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <label 
                    v-for="beneficiary in allBeneficiaries" 
                    :key="beneficiary.id"
                    class="label cursor-pointer justify-start"
                  >
                    <input 
                      type="checkbox" 
                      :value="beneficiary.id"
                      v-model="alert.recipients"
                      class="checkbox checkbox-sm mr-2" 
                    />
                    <span class="label-text">{{ beneficiary.name }} ({{ beneficiary.email }})</span>
                  </label>
                </div>
              </div>
              
              <!-- Trusted Contacts -->
              <div v-if="trustedContacts.length > 0" class="mb-3">
                <h4 class="text-sm font-medium mb-2">Trusted Contacts</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <label 
                    v-for="contact in trustedContacts" 
                    :key="contact.id"
                    class="label cursor-pointer justify-start"
                  >
                    <input 
                      type="checkbox" 
                      :value="contact.id"
                      v-model="alert.recipients"
                      class="checkbox checkbox-sm mr-2" 
                    />
                    <span class="label-text">{{ contact.name }} ({{ contact.email }})</span>
                  </label>
                </div>
              </div>
              
              <!-- Custom Recipients -->
              <div class="mb-3">
                <div class="flex justify-between items-center mb-2">
                  <h4 class="text-sm font-medium">Custom Recipients</h4>
                  <button @click="addCustomRecipient(alert)" class="btn btn-xs btn-outline">
                    Add Custom
                  </button>
                </div>
                
                <div v-for="(custom, customIndex) in alert.custom" :key="customIndex" class="flex gap-2 mb-2">
                  <input 
                    type="text" 
                    v-model="custom.name"
                    placeholder="Name" 
                    class="input input-sm flex-1" 
                  />
                  <input 
                    type="email" 
                    v-model="custom.email"
                    placeholder="Email" 
                    class="input input-sm flex-1" 
                  />
                  <button 
                    @click="removeCustomRecipient(alert, customIndex)" 
                    class="btn btn-sm btn-ghost text-error"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <button @click="addAlert" class="btn btn-outline w-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Another Alert
          </button>
        </div>
        
        <div class="mt-6">
          <button @click="testEmailSystem" class="btn btn-primary mr-2" :class="{ 'loading': isTestingEmail }">
            {{ isTestingEmail ? 'Testing...' : 'Test Email System' }}
          </button>
          <button @click="saveAlerts" class="btn btn-outline">
            Save Alert Settings
          </button>
        </div>
      </div>
    </div>
    
    <!-- Add Contact Modal -->
    <div class="modal" :class="{ 'modal-open': showAddContactModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Add Trusted Contact</h3>
        
        <form @submit.prevent="saveContact" class="space-y-4">
          <div v-if="contactError" class="alert alert-error text-sm">{{ contactError }}</div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Full Name *</span>
            </label>
            <input 
              type="text" 
              v-model="newContact.name"
              class="input input-bordered w-full" 
              placeholder="John Doe"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Email *</span>
            </label>
            <input 
              type="email" 
              v-model="newContact.email"
              class="input input-bordered w-full" 
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Relationship</span>
            </label>
            <input 
              type="text" 
              v-model="newContact.relationship"
              class="input input-bordered w-full" 
              placeholder="Friend, Family Member, etc."
            />
          </div>
          
          <div class="modal-action">
            <button type="button" @click="closeAddContactModal" class="btn">Cancel</button>
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="!newContact.name || !newContact.email || isAddingContact"
              :class="{ 'loading': isAddingContact }"
            >
              {{ isAddingContact ? 'Adding...' : 'Add Contact' }}
            </button>
          </div>
        </form>
      </div>
      <div class="modal-backdrop" @click="closeAddContactModal"></div>
    </div>
    
    <!-- Delete Contact Modal -->
    <div class="modal" :class="{ 'modal-open': showDeleteContactModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Remove Trusted Contact</h3>
        <p>Are you sure you want to remove this trusted contact? They will no longer be able to verify your status.</p>
        <div class="modal-action">
          <button @click="showDeleteContactModal = false" class="btn">Cancel</button>
          <button 
            @click="deleteContact" 
            class="btn btn-error"
            :class="{ 'loading': isDeletingContact }"
            :disabled="isDeletingContact"
          >
            {{ isDeletingContact ? 'Removing...' : 'Remove' }}
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showDeleteContactModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { TrustedContact } from '~/types';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useUserStore } from '~/stores/userStore';
import { useEmailService } from '~/utils/emailService';

definePageMeta({
  layout: 'dashboard'
});

const beneficiaryStore = useBeneficiaryStore();
const userStore = useUserStore();
const user = useSupabaseUser();
const supabase = useSupabaseClient();
const emailService = useEmailService();

// Loading states
const isLoading = ref(true);
const isSaving = ref(false);
const isTestingEmail = ref(false);
const isAddingContact = ref(false);
const isDeletingContact = ref(false);
const isResending = ref(false);

// Modals
const showAddContactModal = ref(false);
const showDeleteContactModal = ref(false);
const contactToDelete = ref<string | null>(null);
const contactError = ref('');

// Form state
const hasChanges = ref(false);

// Trigger settings with better defaults
const triggerSettings = ref({
  id: '',
  userId: '',
  inactivityPeriod: 1, // Default to 1, but suggest 7
  reminderFrequency: 1, // Default to 1, but suggest 14
  verificationMethod: 'EMAIL',
  isActive: true,
  lastActivity: new Date().toISOString(),
  createdAt: '',
  updatedAt: ''
});

// Trusted contacts
const trustedContacts = ref<TrustedContact[]>([]);

// New contact form
const newContact = ref({
  name: '',
  email: '',
  relationship: ''
});

// Alert system state
const alerts = ref([
  { days: 30, recipients: [] as string[], custom: [] as { name: string; email: string }[] }
]);

// Computed properties
const hasBeneficiaries = computed(() => beneficiaryStore.beneficiaries.length > 0);
const allBeneficiaries = computed(() => beneficiaryStore.beneficiaries);

// Helper functions
const markAsChanged = () => {
  hasChanges.value = true;
};

const adjustInactivity = (delta: number) => {
  const newValue = triggerSettings.value.inactivityPeriod + delta;
  if (newValue >= 1 && newValue <= 365) {
    triggerSettings.value.inactivityPeriod = newValue;
    markAsChanged();
  }
};

const adjustReminder = (delta: number) => {
  const newValue = triggerSettings.value.reminderFrequency + delta;
  if (newValue >= 1 && newValue <= 90) {
    triggerSettings.value.reminderFrequency = newValue;
    markAsChanged();
  }
};

const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

// Fetch data functions
const fetchTriggerSettings = async () => {
  try {
    if (!user.value) return;

    const { data, error } = await supabase
      .from('trigger_settings')
      .select('*')
      .eq('user_id', user.value.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    if (data) {
      triggerSettings.value = {
        id: data.id,
        userId: data.user_id,
        inactivityPeriod: data.inactivity_period,
        reminderFrequency: data.reminder_frequency,
        verificationMethod: data.verification_method,
        isActive: data.is_active,
        lastActivity: data.last_activity,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    }
  } catch (error) {
    console.error('Error fetching trigger settings:', error);
  }
};

const fetchTrustedContacts = async () => {
  try {
    if (!user.value) return;

    const { data, error } = await supabase
      .from('trusted_contacts')
      .select('*')
      .eq('user_id', user.value.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    trustedContacts.value = data.map((contact: any) => ({
      id: contact.id,
      userId: contact.user_id,
      name: contact.name,
      email: contact.email,
      phoneNumber: contact.phone_number,
      relationship: contact.relationship,
      isVerified: contact.is_verified,
      createdAt: contact.created_at,
      updatedAt: contact.updated_at
    }));
  } catch (error) {
    console.error('Error fetching trusted contacts:', error);
  }
};

// Save functions
const saveTriggerSettings = async () => {
  if (!user.value) return;

  isSaving.value = true;
  
  try {
    // Check if record exists first
    const { data: existingData, error: fetchError } = await supabase
      .from('trigger_settings')
      .select('id')
      .eq('user_id', user.value.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError;
    }

    let result;
    if (existingData) {
      // Update existing record
      const { error } = await supabase
        .from('trigger_settings')
        .update({
          inactivity_period: triggerSettings.value.inactivityPeriod,
          reminder_frequency: triggerSettings.value.reminderFrequency,
          verification_method: triggerSettings.value.verificationMethod,
          is_active: triggerSettings.value.isActive,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.value.id);

      if (error) throw error;
    } else {
      // Insert new record
      const { error } = await supabase
        .from('trigger_settings')
        .insert({
          user_id: user.value.id,
          inactivity_period: triggerSettings.value.inactivityPeriod,
          reminder_frequency: triggerSettings.value.reminderFrequency,
          verification_method: triggerSettings.value.verificationMethod,
          is_active: triggerSettings.value.isActive
        });

      if (error) throw error;
    }

    hasChanges.value = false;
    
    // Show success notification
    const notification = document.createElement('div');
    notification.className = 'toast toast-top toast-end';
    notification.innerHTML = `
      <div class="alert alert-success">
        <span>Trigger settings saved successfully!</span>
      </div>
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
    
  } catch (error: any) {
    console.error('Error saving trigger settings:', error);
    alert(`Error saving settings: ${error.message}`);
  } finally {
    isSaving.value = false;
  }
};

const saveContact = async () => {
  if (!user.value || !newContact.value.name || !newContact.value.email) return;

  isAddingContact.value = true;
  contactError.value = '';
  
  try {
    // Check if email already exists
    const { data: existing } = await supabase
      .from('trusted_contacts')
      .select('id')
      .eq('user_id', user.value.id)
      .eq('email', newContact.value.email);

    if (existing && existing.length > 0) {
      contactError.value = 'A contact with this email already exists';
      return;
    }

    // Add new contact to database
    const { data, error } = await supabase
      .from('trusted_contacts')
      .insert({
        user_id: user.value.id,
        name: newContact.value.name,
        email: newContact.value.email,
        relationship: newContact.value.relationship || null,
        is_verified: false
      })
      .select()
      .single();

    if (error) throw error;

    // Add to local state
    trustedContacts.value.unshift({
      id: data.id,
      userId: data.user_id,
      name: data.name,
      email: data.email,
      phoneNumber: data.phone_number,
      relationship: data.relationship,
      isVerified: data.is_verified,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    });

    // Send notification email
    await userStore.fetchProfile();
    const userName = userStore.fullName || user.value.email;
    
    await emailService.sendTrustedContactNotification(
      newContact.value.email,
      newContact.value.name,
      user.value.email,
      userName || 'Volnt User'
    );

    // Reset form and close modal
    closeAddContactModal();
    
    // Show success notification
    const notification = document.createElement('div');
    notification.className = 'toast toast-top toast-end';
    notification.innerHTML = `
      <div class="alert alert-success">
        <span>Trusted contact added and notification sent!</span>
      </div>
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
    
  } catch (error: any) {
    console.error('Error adding trusted contact:', error);
    contactError.value = `Error adding contact: ${error.message}`;
  } finally {
    isAddingContact.value = false;
  }
};

const closeAddContactModal = () => {
  showAddContactModal.value = false;
  newContact.value = { name: '', email: '', relationship: '' };
  contactError.value = '';
};

const confirmDeleteContact = (id: string) => {
  contactToDelete.value = id;
  showDeleteContactModal.value = true;
};

const deleteContact = async () => {
  if (!contactToDelete.value) return;

  isDeletingContact.value = true;

  try {
    const { error } = await supabase
      .from('trusted_contacts')
      .delete()
      .eq('id', contactToDelete.value);

    if (error) throw error;

    // Remove from local state
    trustedContacts.value = trustedContacts.value.filter(c => c.id !== contactToDelete.value);
    
    showDeleteContactModal.value = false;
    contactToDelete.value = null;

    // Show success notification
    const notification = document.createElement('div');
    notification.className = 'toast toast-top toast-end';
    notification.innerHTML = `
      <div class="alert alert-success">
        <span>Trusted contact removed successfully!</span>
      </div>
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);

  } catch (error: any) {
    console.error('Error deleting contact:', error);
    alert(`Error removing contact: ${error.message}`);
  } finally {
    isDeletingContact.value = false;
  }
};

const resendInvitation = async (id: string) => {
  const contact = trustedContacts.value.find(c => c.id === id);
  if (!contact || !user.value) return;

  isResending.value = true;
  
  try {
    await userStore.fetchProfile();
    const userName = userStore.fullName || user.value.email;
    
    await emailService.sendTrustedContactNotification(
      contact.email,
      contact.name,
      user.value.email,
      userName || 'Volnt User'
    );

    // Show success notification
    const notification = document.createElement('div');
    notification.className = 'toast toast-top toast-end';
    notification.innerHTML = `
      <div class="alert alert-success">
        <span>Invitation resent to ${contact.name}!</span>
      </div>
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
    
  } catch (error: any) {
    console.error('Error resending invitation:', error);
    alert(`Error resending invitation: ${error.message}`);
  } finally {
    isResending.value = false;
  }
};

// Alert system functions
const addAlert = () => {
  alerts.value.push({ days: 30, recipients: [], custom: [] });
};

const removeAlert = (idx: number) => {
  if (alerts.value.length > 1) alerts.value.splice(idx, 1);
};

const incrementDays = (alert: any) => {
  if (alert.days < 120) alert.days++;
};

const decrementDays = (alert: any) => {
  if (alert.days > 1) alert.days--;
};

const addCustomRecipient = (alert: any) => {
  alert.custom.push({ name: '', email: '' });
};

const removeCustomRecipient = (alert: any, idx: number) => {
  alert.custom.splice(idx, 1);
};

const testEmailSystem = async () => {
  isTestingEmail.value = true;
  
  try {
    if (!user.value) {
      throw new Error('User not authenticated');
    }

    await userStore.fetchProfile();
    const userName = userStore.fullName || user.value.email;
    
    // Prepare test recipients
    const testRecipients = [];
    
    // Add user's own email for testing
    testRecipients.push({
      email: user.value.email,
      name: userName || 'Test User'
    });
    
    // Add any custom recipients from alerts
    alerts.value.forEach(alert => {
      alert.custom.forEach(custom => {
        if (custom.email && custom.name) {
          testRecipients.push({
            email: custom.email,
            name: custom.name
          });
        }
      });
    });
    
    if (testRecipients.length === 0) {
      alert('No recipients configured for testing. Add some custom recipients first.');
      return;
    }
    
    // Send test inactivity warning
    const result = await emailService.sendInactivityWarning(
      testRecipients,
      userName || 'Test User',
      user.value.email,
      30, // 30 days inactive
      'This is a test email from the Volnt trigger system. Your actual alerts will be sent based on your configured settings.'
    );
    
    if (result.success) {
      alert(`Test emails sent successfully to ${testRecipients.length} recipients!`);
    } else {
      throw new Error(result.error?.message || 'Failed to send test emails');
    }
    
  } catch (error: any) {
    console.error('Error testing email system:', error);
    alert(`Error testing email system: ${error.message}`);
  } finally {
    isTestingEmail.value = false;
  }
};

const saveAlerts = async () => {
  // Placeholder: Here you would save the alert configuration to Supabase
  alert('Alert settings saved! (Database integration coming soon)');
};

// Fetch data on mount
onMounted(async () => {
  try {
    await Promise.all([
      fetchTriggerSettings(),
      fetchTrustedContacts(),
      beneficiaryStore.fetchBeneficiaries()
    ]);
  } finally {
    isLoading.value = false;
  }
});
</script>