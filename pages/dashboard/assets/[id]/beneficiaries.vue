<template>
  <div>
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Manage Beneficiaries</h1>
        <p class="text-base-content/70">{{ asset?.name || 'Asset' }}</p>
      </div>
      
      <NuxtLink :to="`/dashboard/assets`" class="btn btn-sm btn-ghost">
        <Icon icon="mdi:arrow-left" class="h-4 w-4 mr-1" />
        Back to Assets
      </NuxtLink>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Asset Not Found -->
    <div v-else-if="!asset" class="bg-base-100 rounded-lg p-8 text-center">
      <div class="mb-4 flex justify-center">
        <div class="rounded-full bg-error/10 p-3">
          <Icon icon="mdi:alert-triangle" class="h-12 w-12 text-error" />
        </div>
      </div>
      <h3 class="text-xl font-medium mb-2">Asset Not Found</h3>
      <p class="text-base-content/70 mb-6">The asset you're looking for doesn't exist or has been deleted.</p>
      <NuxtLink to="/dashboard/assets" class="btn btn-primary">Back to Assets</NuxtLink>
    </div>

    <!-- Main Content -->
    <div v-else class="space-y-6">
      <!-- Asset Info Card -->
      <div class="card bg-base-100 shadow-md">
        <div class="card-body">
          <div class="flex items-center gap-4">
            <div :class="`avatar bg-${typeColor}-100 p-3 rounded-full`">
              <div class="w-12 h-12 text-base-content flex items-center justify-center">
                <Icon :icon="typeIcon" class="w-6 h-6" />
              </div>
            </div>
            <div class="flex-1">
              <h2 class="text-xl font-semibold">{{ asset.name }}</h2>
              <p class="text-base-content/70">{{ assetTypeLabel }}</p>
              <div v-if="asset.value" class="badge badge-primary mt-1">
                {{ formatCurrency(asset.value, asset.currency || 'USD') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Current Assignments -->
      <div class="card bg-base-100 shadow-md">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Beneficiary Assignments</h3>
            <button @click="showAddModal = true" class="btn btn-sm btn-primary">
              <Icon icon="mdi:plus" class="h-4 w-4 mr-1" />
              Add Beneficiary
            </button>
          </div>

          <!-- Assignments List -->
          <div v-if="currentAssignments.length > 0" class="space-y-3">
            <div 
              v-for="assignment in currentAssignments" 
              :key="assignment.beneficiaryId"
              class="flex items-center justify-between p-4 border border-base-300 rounded-lg"
            >
              <div class="flex items-center">
                <div class="avatar mr-4">
                  <div class="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center text-lg font-medium">
                    {{ getBeneficiaryInitials(assignment.beneficiaryId) }}
                  </div>
                </div>
                <div>
                  <div class="font-semibold">{{ getBeneficiaryName(assignment.beneficiaryId) }}</div>
                  <div class="text-sm text-base-content/70">{{ getBeneficiaryRelationship(assignment.beneficiaryId) }}</div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                  <input 
                    type="number" 
                    v-model.number="assignment.percentage"
                    @input="validateAndSavePercentage(assignment)"
                    min="1" 
                    max="100" 
                    class="input input-sm input-bordered w-20 text-center"
                  />
                  <span class="text-sm font-medium">%</span>
                </div>
                
                <div class="dropdown dropdown-end">
                  <label tabindex="0" class="btn btn-sm btn-ghost btn-circle">
                    <Icon icon="mdi:dots-vertical" class="w-4 h-4" />
                  </label>
                  <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-48">
                    <li><a @click="editBeneficiary(assignment.beneficiaryId)">Edit Beneficiary</a></li>
                    <li><a @click="confirmRemoveAssignment(assignment.beneficiaryId)" class="text-error">Remove Assignment</a></li>
                  </ul>
                </div>
              </div>
            </div>
            
            <!-- Percentage Summary -->
            <div class="mt-4 p-4 bg-base-200 rounded-lg">
              <div class="flex justify-between items-center">
                <span class="font-medium">Total Allocated:</span>
                <span class="font-bold text-lg" :class="{ 'text-error': totalPercentage > 100, 'text-success': totalPercentage === 100 }">
                  {{ totalPercentage }}%
                </span>
              </div>
              <div v-if="totalPercentage > 100" class="text-error text-sm mt-1">
                ⚠️ Total percentage cannot exceed 100%
              </div>
              <div v-else-if="totalPercentage < 100" class="text-warning text-sm mt-1">
                {{ 100 - totalPercentage }}% remaining to allocate
              </div>
              <div v-else class="text-success text-sm mt-1">
                ✓ Asset fully allocated
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <div class="mb-4 flex justify-center">
              <div class="rounded-full bg-base-200 p-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-base-content/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <h4 class="text-lg font-medium mb-2">No Beneficiaries Assigned</h4>
            <p class="text-base-content/70 mb-4">This asset hasn't been assigned to any beneficiaries yet.</p>
            <button @click="showAddModal = true" class="btn btn-primary">
              Assign First Beneficiary
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Beneficiary Modal -->
    <UiAssetBeneficiaryModal
      :is-open="showAddModal"
      :asset="asset"
      @close="showAddModal = false"
      @saved="onAssignmentSaved"
    />

    <!-- Remove Assignment Confirmation Modal -->
    <div class="modal" :class="{ 'modal-open': showRemoveModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Remove Assignment</h3>
        <p>Are you sure you want to remove this beneficiary assignment? This action cannot be undone.</p>
        <div class="modal-action">
          <button @click="showRemoveModal = false" class="btn">Cancel</button>
          <button @click="removeAssignment" class="btn btn-error" :class="{ 'loading': beneficiaryStore.isLoading }">
            Remove
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showRemoveModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { Asset, AssetType } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const route = useRoute();
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const isLoading = ref(true);
const showAddModal = ref(false);
const showRemoveModal = ref(false);
const beneficiaryToRemove = ref<string | null>(null);

const assetId = route.params.id as string;

// Computed properties
const asset = computed(() => assetStore.assets.find(a => a.id === assetId));

const currentAssignments = computed(() => {
  return (asset.value?.beneficiaries || []).map(b => ({
    beneficiaryId: b.id,
    percentage: b.percentage || 0
  }));
});

const totalPercentage = computed(() => {
  return currentAssignments.value.reduce((sum, assignment) => sum + assignment.percentage, 0);
});

const assetTypeLabel = computed(() => {
  if (!asset.value) return '';
  
  const typeMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'Digital Account',
    CRYPTOCURRENCY: 'Cryptocurrency',
    SOCIAL_MEDIA: 'Social Media',
    FINANCIAL_ACCOUNT: 'Financial Account',
    REAL_ESTATE: 'Real Estate',
    VEHICLE: 'Vehicle',
    COLLECTIBLE: 'Collectible',
    INSURANCE: 'Insurance',
    INTELLECTUAL_PROPERTY: 'Intellectual Property',
    PERSONAL_BELONGING: 'Personal Belonging',
    CUSTOM: 'Custom'
  };
  
  return typeMap[asset.value.type] || 'Asset';
});

const typeColor = computed(() => {
  if (!asset.value) return 'blue';
  
  const colorMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'blue',
    CRYPTOCURRENCY: 'yellow',
    SOCIAL_MEDIA: 'purple',
    FINANCIAL_ACCOUNT: 'green',
    REAL_ESTATE: 'amber',
    VEHICLE: 'sky',
    COLLECTIBLE: 'pink',
    INSURANCE: 'emerald',
    INTELLECTUAL_PROPERTY: 'indigo',
    PERSONAL_BELONGING: 'orange',
    CUSTOM: 'gray'
  };
  
  return colorMap[asset.value.type] || 'blue';
});

const typeIcon = computed(() => {
  if (!asset.value) return 'mdi:tag';

  const iconMap: Record<AssetType, string> = {
    DIGITAL_ACCOUNT: 'mdi:monitor',
    CRYPTOCURRENCY: 'mdi:bitcoin',
    SOCIAL_MEDIA: 'mdi:account-group',
    FINANCIAL_ACCOUNT: 'mdi:credit-card',
    REAL_ESTATE: 'mdi:home',
    VEHICLE: 'mdi:car',
    COLLECTIBLE: 'mdi:treasure-chest',
    INSURANCE: 'mdi:shield-check',
    INTELLECTUAL_PROPERTY: 'mdi:lightbulb',
    PERSONAL_BELONGING: 'mdi:bag-personal',
    CUSTOM: 'mdi:tag'
  };

  return iconMap[asset.value.type] || iconMap.CUSTOM;
});

// Helper functions
const getBeneficiaryName = (beneficiaryId: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId);
  return beneficiary?.name || 'Unknown';
};

const getBeneficiaryInitials = (beneficiaryId: string) => {
  const name = getBeneficiaryName(beneficiaryId);
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const getBeneficiaryRelationship = (beneficiaryId: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId);
  return beneficiary?.relationship || 'Beneficiary';
};

const formatCurrency = (value: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0
  }).format(value);
};

// Actions
const validateAndSavePercentage = async (assignment: { beneficiaryId: string; percentage: number }) => {
  if (assignment.percentage < 1) assignment.percentage = 1;
  if (assignment.percentage > 100) assignment.percentage = 100;
  
  // Save the updated percentage
  await beneficiaryStore.assignAsset(assignment.beneficiaryId, assetId, assignment.percentage);
  
  // Refresh data
  await assetStore.fetchAssets();
};

const editBeneficiary = (beneficiaryId: string) => {
  navigateTo(`/dashboard/beneficiaries/edit/${beneficiaryId}`);
};

const confirmRemoveAssignment = (beneficiaryId: string) => {
  beneficiaryToRemove.value = beneficiaryId;
  showRemoveModal.value = true;
};

const removeAssignment = async () => {
  if (beneficiaryToRemove.value) {
    await beneficiaryStore.removeAssetAssignment(beneficiaryToRemove.value, assetId);
    await assetStore.fetchAssets();
    showRemoveModal.value = false;
    beneficiaryToRemove.value = null;
  }
};

const onAssignmentSaved = () => {
  showAddModal.value = false;
};

// Fetch data on mount
onMounted(async () => {
  try {
    await Promise.all([
      assetStore.fetchAssets(),
      beneficiaryStore.fetchBeneficiaries()
    ]);
  } finally {
    isLoading.value = false;
  }
});
</script>
