<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Add New Asset</h1>
        <p class="text-base-content/70">Document a new asset for your digital will</p>
      </div>
      
      <NuxtLink to="/dashboard/assets" class="btn btn-sm btn-ghost">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Assets
      </NuxtLink>
    </div>
    
    <div class="bg-base-100 shadow-md rounded-lg p-6">
      <form @submit.prevent="saveAsset" class="space-y-6">
        <!-- Basic Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Basic Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Name *</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.name"
                class="input input-bordered w-full" 
                placeholder="e.g., Bitcoin Wallet, Gmail Account"
                required
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Type *</span>
              </label>
              <select v-model="assetData.type" class="select select-bordered w-full" required>
                <option value="">Select asset type</option>
                <option v-for="type in assetTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="assetData.description"
              class="textarea textarea-bordered w-full" 
              placeholder="Brief description of this asset"
              rows="3"
            ></textarea>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Estimated Value</span>
              </label>
              <input 
                type="number" 
                v-model.number="assetData.value"
                class="input input-bordered w-full" 
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Currency</span>
              </label>
              <select v-model="assetData.currency" class="select select-bordered w-full">
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Location</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.location"
                class="input input-bordered w-full" 
                placeholder="Physical or digital location"
              />
            </div>
          </div>
        </div>
        
        <!-- Account Details -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Account Details</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Provider/Institution</span>
              </label>
              <input 
                type="text" 
                v-model="accountDetails.provider"
                class="input input-bordered w-full" 
                placeholder="e.g., Google, Bank of America"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Account Number/ID</span>
              </label>
              <input 
                type="text" 
                v-model="accountDetails.accountNumber"
                class="input input-bordered w-full" 
                placeholder="Account identifier"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Username/Email</span>
              </label>
              <input 
                type="text" 
                v-model="accountDetails.username"
                class="input input-bordered w-full" 
                placeholder="Login username or email"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Website/URL</span>
              </label>
              <input 
                type="url" 
                v-model="accountDetails.url"
                class="input input-bordered w-full" 
                placeholder="https://example.com"
              />
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Additional Notes</span>
            </label>
            <textarea 
              v-model="accountDetails.notes"
              class="textarea textarea-bordered w-full" 
              placeholder="Any additional information about this account"
              rows="2"
            ></textarea>
          </div>
        </div>
        
        <!-- Sensitive Information -->
        <div class="card bg-warning/5 border border-warning/20 p-4">
          <div class="flex items-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-warning mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <h3 class="font-semibold text-lg">Sensitive Information</h3>
          </div>
          
          <div class="alert alert-warning mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span>This information will be encrypted and stored securely. Only you and your designated beneficiaries will have access.</span>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Password</span>
              </label>
              <div class="relative">
                <input 
                  :type="showPassword ? 'text' : 'password'"
                  v-model="credentials.password"
                  class="input input-bordered w-full pr-10" 
                  placeholder="Account password"
                />
                <button 
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7A9.97 9.97 0 014.02 8.971m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
              </div>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">PIN/Security Code</span>
              </label>
              <input 
                type="password" 
                v-model="credentials.pin"
                class="input input-bordered w-full" 
                placeholder="PIN or security code"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Recovery Phrase/Seed</span>
              </label>
              <textarea 
                v-model="credentials.recoveryPhrase"
                class="textarea textarea-bordered w-full" 
                placeholder="12 or 24 word recovery phrase"
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Private Keys</span>
              </label>
              <textarea 
                v-model="credentials.privateKeys"
                class="textarea textarea-bordered w-full" 
                placeholder="Private keys or access codes"
                rows="3"
              ></textarea>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Additional Security Information</span>
            </label>
            <textarea 
              v-model="credentials.additionalInfo"
              class="textarea textarea-bordered w-full" 
              placeholder="Any other sensitive information needed to access this asset"
              rows="2"
            ></textarea>
          </div>
        </div>
        
        <!-- Form Actions -->
        <div class="flex justify-end space-x-4">
          <NuxtLink to="/dashboard/assets" class="btn btn-ghost">
            Cancel
          </NuxtLink>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :class="{ 'loading': assetStore.isLoading }"
            :disabled="assetStore.isLoading || !assetData.name || !assetData.type"
          >
            {{ assetStore.isLoading ? 'Saving...' : 'Save Asset' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAssetStore } from '~/stores/assetStore';
import type { AssetType } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const assetStore = useAssetStore();

// Form data
const assetData = ref({
  name: '',
  type: '' as AssetType | '',
  description: '',
  value: undefined as number | undefined,
  currency: 'USD',
  location: ''
});

const accountDetails = ref({
  provider: '',
  accountNumber: '',
  username: '',
  url: '',
  notes: ''
});

const credentials = ref({
  password: '',
  pin: '',
  recoveryPhrase: '',
  privateKeys: '',
  additionalInfo: ''
});

const showPassword = ref(false);

// Asset types
const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

const saveAsset = async () => {
  if (!assetData.value.name || !assetData.value.type) return;
  
  // Prepare sensitive data
  const sensitiveData = {
    accountDetails: {
      provider: accountDetails.value.provider,
      accountNumber: accountDetails.value.accountNumber,
      username: accountDetails.value.username,
      url: accountDetails.value.url,
      notes: accountDetails.value.notes
    },
    credentials: {
      password: credentials.value.password,
      pin: credentials.value.pin,
      recoveryPhrase: credentials.value.recoveryPhrase,
      privateKeys: credentials.value.privateKeys,
      additionalInfo: credentials.value.additionalInfo
    }
  };
  
  // Remove empty values
  Object.keys(sensitiveData.accountDetails).forEach(key => {
    if (!sensitiveData.accountDetails[key as keyof typeof sensitiveData.accountDetails]) {
      delete sensitiveData.accountDetails[key as keyof typeof sensitiveData.accountDetails];
    }
  });
  
  Object.keys(sensitiveData.credentials).forEach(key => {
    if (!sensitiveData.credentials[key as keyof typeof sensitiveData.credentials]) {
      delete sensitiveData.credentials[key as keyof typeof sensitiveData.credentials];
    }
  });
  
  const asset = await assetStore.createAsset(
    {
      name: assetData.value.name,
      type: assetData.value.type as AssetType,
      description: assetData.value.description,
      value: assetData.value.value,
      currency: assetData.value.currency,
      location: assetData.value.location
    },
    sensitiveData
  );
  
  if (asset) {
    navigateTo('/dashboard/assets');
  }
};
</script>