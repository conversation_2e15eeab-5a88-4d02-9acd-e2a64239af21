<template>
  <div class="min-h-screen bg-base-200 flex flex-col items-center py-16">
    <h1 class="text-4xl font-bold mb-8">Our Pricing</h1>
    <div class="flex flex-col md:flex-row gap-8">
      <!-- Basic Plan -->
      <div class="card w-80 bg-base-100 shadow-xl">
        <div class="card-body items-center text-center">
          <h2 class="card-title">Basic</h2>
					<!-- TODO: Update price to 55 after test -->
          <p class="text-4xl font-extrabold my-4">$49.90<span class="text-base font-normal">one time fee</span></p>
          <ul class="mb-6 space-y-2">
            <li>Use forever</li>
            <li>Full access to all features</li>
					  <li>Unlimited assets and beneficiaries</li>
            <li>Basic AI assistance</li>
          </ul>
          <button class="btn btn-primary w-full" @click="checkout('price_basic')">
            Buy Now
          </button>
        </div>
      </div>
      <!-- Pro Plan -->
      <div class="card w-80 bg-primary text-primary-content shadow-xl border-2 border-primary">
        <div class="card-body items-center text-center">
          <h2 class="card-title">Pro</h2>
          <p class="text-4xl font-extrabold my-4">$29<span class="text-base font-normal">/mo</span></p>
          <ul class="mb-6 space-y-2">
            <li>✔️ Everything in Basic</li>
            <li>✔️ Pro Feature 1</li>
            <li>✔️ Placeholder feature</li>
          </ul>
          <button class="btn btn-accent w-full" @click="checkout('price_pro')">
            Buy Now
          </button>
        </div>
      </div>
    </div>
		<div class="mt-12">
			<p class="text-sm text-center">
				Volnt 
			</p>
		</div>
  </div>
</template>

<script setup>
	import { loadStripe } from '@stripe/stripe-js'

	const checkout = async () => {
		// Call your API route to create a Stripe Checkout session
		const res = await fetch('/api/stripe/checkout', {
			method: 'POST',
			body: JSON.stringify({
				priceId: 'prod_SW8sd7X9KtuuTY',
			}),
			headers: {
				'Content-Type': 'application/json',
			},
		})
		const { sessionId } = await res.json()

		// Redirect to Stripe Checkout
		const stripe = await loadStripe(process.env.STRIPE_PUBLIC_TEST_KEY) // Your Stripe publishable key
		await stripe.redirectToCheckout({ sessionId })
	}
</script>