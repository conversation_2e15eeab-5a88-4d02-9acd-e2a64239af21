export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  emergencyContact?: EmergencyContact;
  securityQuestions?: SecurityQuestion[];
  createdAt: string;
  updatedAt: string;
}

export interface EmergencyContact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber: string;
  relationship: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SecurityQuestion {
  id: string;
  userId: string;
  question: string;
  answer: string;
  createdAt: string;
  updatedAt: string;
}

export interface Asset {
  id: string;
  userId: string;
  type: AssetType;
  name: string;
  description?: string;
  value?: number;
  currency?: string;
  accountDetails?: AccountDetails;
  credentials?: EncryptedCredentials;
  location?: string;
  documents?: Document[];
  beneficiaries: Beneficiary[];
  createdAt: string;
  updatedAt: string;
}

export enum AssetType {
  DigitalAccount = 'DIGITAL_ACCOUNT',
  Cryptocurrency = 'CRYPTOCURRENCY',
  SocialMedia = 'SOCIAL_MEDIA',
  FinancialAccount = 'FINANCIAL_ACCOUNT',
  RealEstate = 'REAL_ESTATE',
  Vehicle = 'VEHICLE',
  Collectible = 'COLLECTIBLE',
  Insurance = 'INSURANCE',
  Intellectual = 'INTELLECTUAL_PROPERTY',
  Personal = 'PERSONAL_BELONGING',
  Custom = 'CUSTOM'
}

export interface AccountDetails {
  provider?: string;
  accountNumber?: string;
  username?: string;
  url?: string;
  notes?: string;
}

export interface EncryptedCredentials {
  encryptedData: string;
  iv: string;
}

export interface Document {
  id: string;
  assetId: string;
  name: string;
  type: string;
  url: string;
  createdAt: string;
  updatedAt: string;
}

export interface Beneficiary {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber?: string;
  relationship?: string;
  percentage?: number;
  accessLevel: AccessLevel;
  assets: { assetId: string; percentage: number }[];
  notificationPreference: NotificationPreference;
  isDigitalExecutor: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum AccessLevel {
  Full = 'FULL',
  ReadOnly = 'READ_ONLY',
  Limited = 'LIMITED'
}

export enum NotificationPreference {
  Email = 'EMAIL',
  SMS = 'SMS',
  Both = 'BOTH',
  None = 'NONE'
}

export interface TriggerSettings {
  id: string;
  userId: string;
  inactivityPeriod: number; // in days
  reminderFrequency: number; // in days
  verificationMethod: VerificationMethod;
  trustedContacts: TrustedContact[];
  confirmationSteps: ConfirmationStep[];
  isActive: boolean;
  lastActivity: string;
  createdAt: string;
  updatedAt: string;
}

export enum VerificationMethod {
  Email = 'EMAIL',
  SMS = 'SMS',
  SecurityQuestions = 'SECURITY_QUESTIONS',
  TrustedContacts = 'TRUSTED_CONTACTS'
}

export interface TrustedContact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber?: string;
  relationship?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ConfirmationStep {
  id: string;
  triggerId: string;
  type: ConfirmationType;
  isCompleted: boolean;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export enum ConfirmationType {
  UserResponse = 'USER_RESPONSE',
  ContactVerification = 'CONTACT_VERIFICATION',
  SecurityQuestion = 'SECURITY_QUESTION',
  WaitingPeriod = 'WAITING_PERIOD'
}

export interface WillDocument {
  id: string;
  userId: string;
  title: string;
  content: string;
  lastEdited: string;
  isPublished: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}