/*
  # Add source and beneficiary_id columns to trusted_contacts

  1. Changes
    - Add `source` column to track if contact is from beneficiary or custom
    - Add `beneficiary_id` column to link to beneficiaries table
    - Add check constraint for source values
    - Update existing data to have 'custom' source
*/

-- Add source column with default value
ALTER TABLE trusted_contacts 
ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'custom';

-- Add beneficiary_id column
ALTER TABLE trusted_contacts 
ADD COLUMN IF NOT EXISTS beneficiary_id UUID REFERENCES beneficiaries(id) ON DELETE CASCADE;

-- Add check constraint for source values
ALTER TABLE trusted_contacts 
ADD CONSTRAINT IF NOT EXISTS trusted_contacts_source_check 
CHECK (source IN ('beneficiary', 'custom'));

-- Update existing records to have 'custom' source
UPDATE trusted_contacts 
SET source = 'custom' 
WHERE source IS NULL;