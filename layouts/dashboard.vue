<template>
  <div class="min-h-screen bg-base-200">
    <AppHeader />
    
    <div class="flex">
      <DashboardSidebar :is-open="isSidebarOpen" @toggle="toggleSidebar" />
      
      <main class="flex-1 p-4 md:p-6 transition-all duration-300" :class="{ 'md:ml-64': isSidebarOpen, 'ml-0': !isSidebarOpen }">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const isSidebarOpen = ref(true);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};
</script>