# Volnt Documentation Index

Quick reference guide to all documentation files in the Volnt codebase.

## 📚 Core Documentation

### [README.md](./README.md)
**Main documentation entry point**
- Project overview and quick start guide
- Documentation structure and navigation
- Technology stack summary
- Development workflow overview

### [ARCHITECTURE.md](./ARCHITECTURE.md)
**System architecture and design**
- Technology stack details (Nuxt 3, Vue 3, Supabase)
- Project structure and organization
- Data flow and security architecture
- Performance and deployment considerations

### [STORES.md](./STORES.md)
**Pinia state management**
- userStore: Authentication and profile management
- assetStore: Asset operations and management
- beneficiaryStore: Beneficiary and assignment management
- willStore: Will document operations
- Store integration patterns and best practices

### [COMPONENTS.md](./COMPONENTS.md)
**UI component library**
- App components (Header, Sidebar)
- Dashboard components (CompletionCard)
- Editor components (EnhancedWillEditor, Modals)
- UI components (AssetCard, AssetBeneficiaryModal)
- Component patterns and usage examples

### [PAGES.md](./PAGES.md)
**Application pages and routing**
- Route structure and navigation flow
- Authentication pages (login, register)
- Dashboard pages (overview, will, assets, beneficiaries)
- Page-specific functionality and data flow
- User interaction patterns

### [TYPES.md](./TYPES.md)
**TypeScript type definitions**
- Core entity types (User, Asset, Beneficiary)
- Enum definitions (AssetType, AccessLevel)
- Store state interfaces
- Component prop types
- Type relationships and validation

### [DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)
**Developer onboarding and workflow**
- Setup and installation instructions
- Recommended learning path for new developers
- Development workflow and best practices
- Testing strategies and debugging tips
- Contributing guidelines

## 🔧 Feature-Specific Documentation

### [EDIT_MODALS_GUIDE.md](./EDIT_MODALS_GUIDE.md)
**Edit functionality implementation**
- EditAssetModal and EditBeneficiaryModal components
- Integration with assets and beneficiaries pages
- Modal patterns and event handling
- Store method usage for updates

### [ICONIFY_MIGRATION_SUMMARY.md](./ICONIFY_MIGRATION_SUMMARY.md)
**Icon system migration**
- Migration from Heroicons to Iconify
- Updated icon usage patterns
- Component updates and changes
- Benefits of the new icon system

### [BENEFICIARY_ASSIGNMENT_GUIDE.md](./BENEFICIARY_ASSIGNMENT_GUIDE.md)
**Asset-beneficiary relationship management**
- Assignment patterns and workflows
- Percentage-based distribution system
- Modal-based assignment management
- Data flow and validation

## 📖 Documentation Usage Guide

### For New Developers
**Recommended reading order:**

1. **[README.md](./README.md)** - Start here for project overview
2. **[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - Setup and workflow
3. **[TYPES.md](./TYPES.md)** - Understand data structures
4. **[STORES.md](./STORES.md)** - Learn business logic
5. **[PAGES.md](./PAGES.md)** - Application flow
6. **[COMPONENTS.md](./COMPONENTS.md)** - UI building blocks
7. **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Big picture understanding

### For Specific Tasks

#### Working with Assets
- [TYPES.md](./TYPES.md) - Asset interface and AssetType enum
- [STORES.md](./STORES.md) - assetStore actions and getters
- [COMPONENTS.md](./COMPONENTS.md) - AssetCard and CreateAssetModal
- [EDIT_MODALS_GUIDE.md](./EDIT_MODALS_GUIDE.md) - EditAssetModal usage

#### Working with Beneficiaries
- [TYPES.md](./TYPES.md) - Beneficiary interface and relationships
- [STORES.md](./STORES.md) - beneficiaryStore operations
- [COMPONENTS.md](./COMPONENTS.md) - CreateBeneficiaryModal
- [BENEFICIARY_ASSIGNMENT_GUIDE.md](./BENEFICIARY_ASSIGNMENT_GUIDE.md) - Assignment patterns

#### Working with the Will Editor
- [COMPONENTS.md](./COMPONENTS.md) - EnhancedWillEditor component
- [STORES.md](./STORES.md) - willStore operations
- [PAGES.md](./PAGES.md) - Will editor page functionality

#### UI Development
- [COMPONENTS.md](./COMPONENTS.md) - Component patterns and examples
- [ARCHITECTURE.md](./ARCHITECTURE.md) - Styling and design system
- [ICONIFY_MIGRATION_SUMMARY.md](./ICONIFY_MIGRATION_SUMMARY.md) - Icon usage

#### Database and API Work
- [ARCHITECTURE.md](./ARCHITECTURE.md) - Database schema and security
- [STORES.md](./STORES.md) - API integration patterns
- [TYPES.md](./TYPES.md) - Data interfaces and relationships

## 🔍 Quick Reference

### Key Files to Understand
```
types/index.ts              # All TypeScript interfaces
stores/                     # Business logic and state management
├── userStore.ts           # User authentication and profile
├── assetStore.ts          # Asset management
├── beneficiaryStore.ts    # Beneficiary management
└── willStore.ts           # Will document operations

pages/dashboard/            # Main application functionality
├── index.vue              # Dashboard overview
├── will.vue               # Will editor
├── assets/index.vue       # Asset management
└── beneficiaries/index.vue # Beneficiary management

components/editor/          # Rich text editor and modals
├── EnhancedWillEditor.vue # Main will editor
├── CreateAssetModal.vue   # Asset creation
├── CreateBeneficiaryModal.vue # Beneficiary creation
├── EditAssetModal.vue     # Asset editing
└── EditBeneficiaryModal.vue # Beneficiary editing
```

### Common Patterns
- **Modal Components**: Consistent props (show, entity) and events (close, updated)
- **Store Actions**: Async operations with loading states and error handling
- **Form Validation**: Zod schemas with real-time validation
- **Type Safety**: Comprehensive TypeScript interfaces for all data

### Development Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## 📝 Documentation Maintenance

### Updating Documentation
When making changes to the codebase:

1. **Update relevant documentation files** for any architectural changes
2. **Add new components** to COMPONENTS.md with usage examples
3. **Document new types** in TYPES.md with relationships
4. **Update store documentation** for new actions or state changes
5. **Add feature guides** for complex new functionality

### Documentation Standards
- Use clear, descriptive headings
- Include code examples for complex concepts
- Maintain consistent formatting and structure
- Link between related documentation sections
- Keep examples up-to-date with current codebase

---

This index provides a comprehensive overview of all documentation available for the Volnt project. Each file focuses on a specific aspect of the codebase to help developers quickly find the information they need.
