# Core Components Documentation

This document covers the most critical and frequently-used components in the Volnt application.

## Component Organization

```
components/
├── app/              # Global application components
├── dashboard/        # Dashboard-specific components  
├── editor/           # Rich text editor and modals
└── ui/               # Reusable UI components
```

## App Components

### AppHeader.vue
**Location**: `components/app/Header.vue`

**Purpose**: Main navigation header with authentication state and user menu.

**Key Features**:
- Responsive navigation with mobile menu
- User authentication state display
- Profile dropdown with logout functionality
- Logo and brand navigation

**Usage**:
```vue
<AppHeader />
```

**Dependencies**: 
- Supabase user state
- Navigation routing

## Dashboard Components

### DashboardSidebar.vue
**Location**: `components/dashboard/Sidebar.vue`

**Purpose**: Main navigation sidebar for dashboard pages.

**Props**:
- `isOpen: boolean` - Controls sidebar visibility
- `@toggle` - Event emitted when sidebar toggle is clicked

**Key Features**:
- Collapsible sidebar with responsive behavior
- Active route highlighting
- Icon-based navigation menu
- Mobile-friendly overlay

**Usage**:
```vue
<DashboardSidebar 
  :is-open="isSidebarOpen" 
  @toggle="toggleSidebar" 
/>
```

**Menu Items**:
- Overview (`/dashboard`)
- Will Editor (`/dashboard/will`)
- Assets (`/dashboard/assets`)
- Beneficiaries (`/dashboard/beneficiaries`)
- Alerts (`/dashboard/triggers`)
- Profile (`/dashboard/profile`)
- Settings (`/dashboard/settings`)

### CompletionCard.vue
**Location**: `components/dashboard/CompletionCard.vue`

**Purpose**: Displays task completion progress with interactive checklist.

**Props**:
```typescript
{
  title: string;
  description: string;
  percentage: number;
  items: Array<{
    id: string;
    text: string;
    completed: boolean;
  }>;
  actionText: string;
  actionLink: string;
}
```

**Events**:
- `@toggle-item(itemId: string)` - When checklist item is toggled

**Usage**:
```vue
<CompletionCard
  title="Asset Setup"
  description="Add and organize your digital assets"
  :percentage="75"
  :items="assetTasks"
  action-text="Manage Assets"
  action-link="/dashboard/assets"
  @toggle-item="handleToggle"
/>
```

## Editor Components

### EnhancedWillEditor.vue
**Location**: `components/editor/EnhancedWillEditor.vue`

**Purpose**: Rich text editor for will documents with custom asset/beneficiary blocks.

**Props**:
- `modelValue: string` - HTML content of the will
- `placeholder?: string` - Placeholder text

**Events**:
- `@update:modelValue` - Emitted when content changes

**Key Features**:
- TipTap-based rich text editing
- Custom toolbar with formatting options
- Asset and beneficiary block insertion
- Auto-save functionality
- Responsive design

**Custom Extensions**:
- `AssetBlockExtension` - Embeds asset information
- `BeneficiaryBlockExtension` - Embeds beneficiary information
- `InstructionBlockExtension` - Special instruction blocks

**Usage**:
```vue
<EnhancedWillEditor
  v-model="willContent"
  placeholder="Start writing your will here..."
  @update:modelValue="handleContentChange"
/>
```

**Toolbar Features**:
- Text formatting (bold, italic, underline)
- Lists (ordered, unordered)
- Headings (H1, H2, H3)
- Insert dropdowns for assets, beneficiaries, instructions

### CreateAssetModal.vue
**Location**: `components/editor/CreateAssetModal.vue`

**Purpose**: Modal for creating new assets with beneficiary assignment.

**Props**:
- `show: boolean` - Controls modal visibility
- `hasChanges?: boolean` - Tracks unsaved changes

**Events**:
- `@close(hasChanges: boolean)` - Modal close event
- `@created(asset: Asset)` - Asset creation success
- `@changed` - Form data changed

**Form Sections**:
1. **Basic Information**: Name, type, description, value, currency, location
2. **Beneficiary Assignment**: Checkbox selection of beneficiaries

**Asset Types Supported**:
- Digital Account, Cryptocurrency, Social Media
- Financial Account, Real Estate, Vehicle
- Collectible, Insurance, Intellectual Property
- Personal Belonging, Custom

**Usage**:
```vue
<CreateAssetModal
  :show="showModal"
  @close="handleClose"
  @created="handleAssetCreated"
/>
```

### CreateBeneficiaryModal.vue
**Location**: `components/editor/CreateBeneficiaryModal.vue`

**Purpose**: Modal for creating new beneficiaries with asset assignment.

**Props**:
- `show: boolean` - Controls modal visibility
- `hasChanges?: boolean` - Tracks unsaved changes

**Events**:
- `@close(hasChanges: boolean)` - Modal close event
- `@created(beneficiary: Beneficiary)` - Beneficiary creation success

**Form Sections**:
1. **Personal Information**: Name, email, phone, relationship
2. **Access & Permissions**: Access level, notifications, digital executor
3. **Asset Assignments**: Checkbox selection of assets

**Key Features**:
- Digital executor validation (only one allowed)
- Relationship dropdown with common options
- Access level configuration
- Asset assignment with visual feedback

**Usage**:
```vue
<CreateBeneficiaryModal
  :show="showModal"
  @close="handleClose"
  @created="handleBeneficiaryCreated"
/>
```

### EditAssetModal.vue / EditBeneficiaryModal.vue
**Location**: `components/editor/EditAssetModal.vue`, `components/editor/EditBeneficiaryModal.vue`

**Purpose**: Modal components for editing existing assets and beneficiaries.

**Props**:
- `show: boolean` - Controls modal visibility
- `asset: Asset | null` or `beneficiary: Beneficiary | null` - Entity to edit
- `hasChanges?: boolean` - Tracks unsaved changes

**Events**:
- `@close(hasChanges: boolean)` - Modal close event
- `@updated(entity)` - Update success event

**Key Features**:
- Pre-populated forms with existing data
- Change detection and validation
- Relationship management (asset-beneficiary assignments)
- Consistent UI with create modals

## UI Components

### AssetCard.vue
**Location**: `components/ui/AssetCard.vue`

**Purpose**: Displays asset information in a card format with action menu.

**Props**:
- `asset: Asset` - Asset data to display
- `showBeneficiaryCount?: boolean` - Whether to show beneficiary count

**Events**:
- `@edit(assetId: string)` - Edit asset action
- `@delete(assetId: string)` - Delete asset action
- `@view-details(assetId: string)` - View details action
- `@assign-beneficiaries(assetId: string)` - Quick assign action
- `@manage-beneficiaries(assetId: string)` - Manage beneficiaries action

**Features**:
- Asset type icon and color coding
- Value display with currency formatting
- Beneficiary count indicator
- Dropdown action menu
- Responsive design

**Usage**:
```vue
<AssetCard
  :asset="asset"
  show-beneficiary-count
  @edit="editAsset"
  @delete="deleteAsset"
  @assign-beneficiaries="assignBeneficiaries"
/>
```

### AssetBeneficiaryModal.vue
**Location**: `components/ui/AssetBeneficiaryModal.vue`

**Purpose**: Modal for managing beneficiary assignments to a specific asset.

**Props**:
- `isOpen: boolean` - Controls modal visibility
- `asset: Asset | null` - Asset to manage assignments for

**Events**:
- `@close` - Modal close event
- `@saved` - Assignment save success

**Features**:
- Percentage-based assignment system
- Real-time percentage validation
- Add/remove beneficiary assignments
- Visual percentage indicators
- Automatic percentage calculation

**Usage**:
```vue
<AssetBeneficiaryModal
  :is-open="showModal"
  :asset="selectedAsset"
  @close="closeModal"
  @saved="onAssignmentSaved"
/>
```

## Component Patterns

### 1. Modal Pattern
All modals follow a consistent pattern:

```vue
<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
    <div class="bg-base-100 rounded-2xl p-4 shadow-lg max-h-[90vh] overflow-y-auto">
      <!-- Modal content -->
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ show: boolean }>();
const emit = defineEmits(['close', 'action']);
</script>
```

### 2. Form Validation Pattern
Forms use consistent validation and loading states:

```vue
<script setup lang="ts">
const isLoading = ref(false);
const errorMessage = ref('');

const handleSubmit = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  
  try {
    // Form submission logic
  } catch (error) {
    errorMessage.value = error.message;
  } finally {
    isLoading.value = false;
  }
};
</script>
```

### 3. Store Integration Pattern
Components integrate with stores consistently:

```vue
<script setup lang="ts">
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

// Reactive data from stores
const assets = computed(() => assetStore.assets);
const isLoading = computed(() => assetStore.isLoading);

// Actions
const handleAction = async () => {
  await assetStore.someAction();
  // Refresh related data
  await beneficiaryStore.fetchBeneficiaries();
};
</script>
```

### 4. Event Handling Pattern
Components emit events for parent communication:

```vue
<script setup lang="ts">
const emit = defineEmits<{
  close: [hasChanges: boolean];
  created: [entity: Asset | Beneficiary];
  updated: [entity: Asset | Beneficiary];
}>();

// Emit with proper typing
emit('created', newAsset);
emit('close', hasFormChanges());
</script>
```

## Best Practices

### 1. Component Composition
- Keep components focused on single responsibilities
- Use composition API for better TypeScript support
- Extract reusable logic into composables

### 2. Props and Events
- Use TypeScript interfaces for prop definitions
- Emit events for parent communication
- Avoid prop drilling with provide/inject when needed

### 3. Styling
- Use Tailwind CSS classes consistently
- Follow DaisyUI component patterns
- Implement responsive design from mobile-first

### 4. Performance
- Use `computed()` for derived state
- Implement proper key attributes for v-for
- Lazy load heavy components when possible
