# TypeScript Types Documentation

This document covers the main TypeScript interfaces and types used throughout the Volnt application.

## Core Entity Types

### User
```typescript
interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  emergencyContact?: EmergencyContact;
  securityQuestions?: SecurityQuestion[];
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Represents a user account with profile information.

**Key Fields**:
- `id`: Unique identifier from Supabase Auth
- `email`: Primary authentication credential
- `firstName/lastName`: Profile information
- `phoneNumber`: Optional contact information
- `emergencyContact`: Backup contact for account recovery
- `securityQuestions`: Additional security measures

### Asset
```typescript
interface Asset {
  id: string;
  userId: string;
  type: AssetType;
  name: string;
  description?: string;
  value?: number;
  currency?: string;
  accountDetails?: AccountDetails;
  credentials?: EncryptedCredentials;
  location?: string;
  documents?: Document[];
  beneficiaries: Beneficiary[];
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Represents digital or physical assets in the estate.

**Key Fields**:
- `type`: Categorizes the asset (see AssetType enum)
- `value/currency`: Financial valuation
- `accountDetails`: Non-sensitive account information
- `credentials`: Encrypted sensitive data
- `beneficiaries`: Array of assigned beneficiaries with percentages
- `location`: Physical or digital location

### Beneficiary
```typescript
interface Beneficiary {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber?: string;
  relationship?: string;
  percentage?: number;
  accessLevel: AccessLevel;
  assets: { assetId: string; percentage: number }[];
  notificationPreference: NotificationPreference;
  isDigitalExecutor: boolean;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Represents people who will inherit assets.

**Key Fields**:
- `relationship`: Connection to the user (Spouse, Child, etc.)
- `accessLevel`: Level of access to estate information
- `assets`: Array of asset assignments with percentages
- `isDigitalExecutor`: Whether this person can execute the will
- `notificationPreference`: How they want to be contacted

## Enum Types

### AssetType
```typescript
enum AssetType {
  DigitalAccount = 'DIGITAL_ACCOUNT',
  Cryptocurrency = 'CRYPTOCURRENCY',
  SocialMedia = 'SOCIAL_MEDIA',
  FinancialAccount = 'FINANCIAL_ACCOUNT',
  RealEstate = 'REAL_ESTATE',
  Vehicle = 'VEHICLE',
  Collectible = 'COLLECTIBLE',
  Insurance = 'INSURANCE',
  Intellectual = 'INTELLECTUAL_PROPERTY',
  Personal = 'PERSONAL_BELONGING',
  Custom = 'CUSTOM'
}
```

**Categories**:
- **Digital**: DIGITAL_ACCOUNT, CRYPTOCURRENCY, SOCIAL_MEDIA
- **Financial**: FINANCIAL_ACCOUNT, INSURANCE
- **Physical**: REAL_ESTATE, VEHICLE, COLLECTIBLE, PERSONAL_BELONGING, INTELLECTUAL_PROPERTY

### AccessLevel
```typescript
enum AccessLevel {
  Full = 'FULL',
  ReadOnly = 'READ_ONLY',
  Limited = 'LIMITED'
}
```

**Levels**:
- `FULL`: Complete access to all estate information
- `READ_ONLY`: Can view but not modify information
- `LIMITED`: Restricted access to assigned assets only

### NotificationPreference
```typescript
enum NotificationPreference {
  Email = 'EMAIL',
  SMS = 'SMS',
  Both = 'BOTH',
  None = 'NONE'
}
```

## Supporting Types

### AccountDetails
```typescript
interface AccountDetails {
  provider?: string;
  accountNumber?: string;
  username?: string;
  url?: string;
  notes?: string;
}
```

**Purpose**: Non-sensitive account information for assets.

### EncryptedCredentials
```typescript
interface EncryptedCredentials {
  encryptedData: string;
  iv: string;
}
```

**Purpose**: Encrypted sensitive data with initialization vector.

### EmergencyContact
```typescript
interface EmergencyContact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber: string;
  relationship: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Backup contact for account recovery and verification.

### SecurityQuestion
```typescript
interface SecurityQuestion {
  id: string;
  userId: string;
  question: string;
  answer: string;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Additional security for account verification.

## Will and Document Types

### WillDocument
```typescript
interface WillDocument {
  id: string;
  userId: string;
  title: string;
  content: string;
  lastEdited: string;
  isPublished: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Represents the will document with rich text content.

**Key Fields**:
- `content`: HTML content from TipTap editor
- `isPublished`: Whether the will is finalized
- `lastEdited`: Timestamp of last modification

### Document
```typescript
interface Document {
  id: string;
  assetId: string;
  name: string;
  type: string;
  url: string;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: File attachments for assets (deeds, certificates, etc.).

## Trigger and Security Types

### TriggerSettings
```typescript
interface TriggerSettings {
  id: string;
  userId: string;
  inactivityPeriod: number; // in days
  reminderFrequency: number; // in days
  verificationMethod: VerificationMethod;
  trustedContacts: TrustedContact[];
  confirmationSteps: ConfirmationStep[];
  isActive: boolean;
  lastActivity: string;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Configuration for automatic will execution triggers.

### VerificationMethod
```typescript
enum VerificationMethod {
  Email = 'EMAIL',
  SMS = 'SMS',
  SecurityQuestions = 'SECURITY_QUESTIONS',
  TrustedContacts = 'TRUSTED_CONTACTS'
}
```

### TrustedContact
```typescript
interface TrustedContact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phoneNumber?: string;
  relationship?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}
```

**Purpose**: Contacts who can verify user status during trigger process.

## Store State Types

### UserState
```typescript
interface UserState {
  isLoading: boolean;
  profile: {
    firstName: string;
    lastName: string;
    phoneNumber: string | null;
  } | null;
  encryptionKey: string | null;
}
```

### AssetState
```typescript
interface AssetState {
  assets: Asset[];
  isLoading: boolean;
  currentAsset: Asset | null;
}
```

### BeneficiaryState
```typescript
interface BeneficiaryState {
  beneficiaries: Beneficiary[];
  isLoading: boolean;
  currentBeneficiary: Beneficiary | null;
}
```

### WillState
```typescript
interface WillState {
  willDocument: WillDocument | null;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
}
```

## Utility Types

### EmailRecipient
```typescript
interface EmailRecipient {
  email: string;
  name: string;
}
```

### TriggerAlertOptions
```typescript
interface TriggerAlertOptions {
  recipients: EmailRecipient[];
  alertType: 'inactivity_warning' | 'verification_request';
  userName: string;
  userEmail: string;
  daysInactive?: number;
  customMessage?: string;
}
```

## Component Prop Types

### Modal Props
```typescript
interface ModalProps {
  show: boolean;
  hasChanges?: boolean;
}
```

### Asset Card Props
```typescript
interface AssetCardProps {
  asset: Asset;
  showBeneficiaryCount?: boolean;
}
```

### Completion Card Props
```typescript
interface CompletionCardProps {
  title: string;
  description: string;
  percentage: number;
  items: Array<{
    id: string;
    text: string;
    completed: boolean;
  }>;
  actionText: string;
  actionLink: string;
}
```

## Type Relationships

### Asset-Beneficiary Relationship
```typescript
// Assets contain beneficiary assignments
Asset.beneficiaries: Beneficiary[]

// Beneficiaries contain asset assignments
Beneficiary.assets: { assetId: string; percentage: number }[]
```

### User-Entity Relationships
```typescript
// All entities belong to a user
Asset.userId: string
Beneficiary.userId: string
WillDocument.userId: string
TriggerSettings.userId: string
```

## Type Guards and Validation

### Asset Type Checking
```typescript
function isDigitalAsset(asset: Asset): boolean {
  const digitalTypes: AssetType[] = [
    'DIGITAL_ACCOUNT', 
    'CRYPTOCURRENCY', 
    'SOCIAL_MEDIA'
  ];
  return digitalTypes.includes(asset.type);
}
```

### Beneficiary Validation
```typescript
function isValidBeneficiary(beneficiary: Partial<Beneficiary>): boolean {
  return !!(beneficiary.name && beneficiary.email);
}
```

## Best Practices

### 1. Use Strict Types
Always define proper interfaces instead of using `any`:

```typescript
// Good
interface CreateAssetData {
  name: string;
  type: AssetType;
  value?: number;
}

// Avoid
function createAsset(data: any) { }
```

### 2. Optional vs Required Fields
Be explicit about optional fields:

```typescript
interface Asset {
  id: string;           // Required
  name: string;         // Required
  description?: string; // Optional
  value?: number;       // Optional
}
```

### 3. Enum Usage
Use enums for fixed sets of values:

```typescript
// Good
type: AssetType.CRYPTOCURRENCY

// Avoid
type: 'cryptocurrency'
```

### 4. Generic Types
Use generics for reusable patterns:

```typescript
interface ApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

type AssetResponse = ApiResponse<Asset>;
type BeneficiaryResponse = ApiResponse<Beneficiary>;
```
