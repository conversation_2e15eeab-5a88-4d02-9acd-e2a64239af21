# Volnt Architecture Overview

## Project Overview

Volnt is a digital will and testament application built with modern web technologies. It allows users to securely manage their digital assets, designate beneficiaries, and create comprehensive wills that can be automatically executed based on configurable triggers.

## Technology Stack

### Core Framework
- **Nuxt 3**: Full-stack Vue.js framework with SSR/SSG capabilities
- **Vue 3**: Progressive JavaScript framework with Composition API
- **TypeScript**: Type-safe JavaScript development
- **Pinia**: State management for Vue applications

### Backend & Database
- **Supabase**: Backend-as-a-Service providing:
  - PostgreSQL database
  - Authentication
  - Real-time subscriptions
  - Edge functions
  - Row Level Security (RLS)

### UI & Styling
- **Tailwind CSS 4.0**: Utility-first CSS framework
- **DaisyUI**: Component library built on Tailwind CSS
- **Iconify**: Icon system with Material Design Icons

### Rich Text Editing
- **TipTap**: Headless rich text editor built on ProseMirror
- Custom extensions for asset and beneficiary blocks

### Security & Encryption
- **CryptoJS**: Client-side encryption for sensitive data
- **Zod**: Runtime type validation and schema validation

### External Services
- **Resend**: Email delivery service for notifications
- **PostHog**: Analytics and user behavior tracking
- **Pica AI**: AI-powered features (future implementation)

## Project Structure

```
volnt/
├── components/           # Vue components
│   ├── app/             # Global app components (Header, Footer)
│   ├── dashboard/       # Dashboard-specific components
│   ├── editor/          # Rich text editor and modals
│   └── ui/              # Reusable UI components
├── layouts/             # Nuxt layouts
│   ├── default.vue      # Default layout
│   ├── dashboard.vue    # Dashboard layout with sidebar
│   └── auth.vue         # Authentication layout
├── pages/               # File-based routing
│   ├── dashboard/       # Protected dashboard pages
│   ├── auth/            # Authentication pages
│   └── index.vue        # Landing page
├── stores/              # Pinia stores
│   ├── userStore.ts     # User profile and authentication
│   ├── assetStore.ts    # Asset management
│   ├── beneficiaryStore.ts # Beneficiary management
│   └── willStore.ts     # Will document management
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
│   ├── encryption.ts    # Data encryption utilities
│   ├── validation.ts    # Form validation schemas
│   └── emailService.ts  # Email service utilities
├── supabase/            # Database migrations and functions
│   ├── migrations/      # SQL migration files
│   └── functions/       # Edge functions
├── plugins/             # Nuxt plugins
└── assets/              # Static assets and styles
```

## Data Flow Architecture

### 1. Authentication Flow
```
User → Supabase Auth → Profile Creation → Dashboard Access
```

### 2. State Management Flow
```
Component → Pinia Store → Supabase API → Database
                ↓
        Local State Update
```

### 3. Data Encryption Flow
```
Sensitive Data → Client Encryption → Supabase Storage → Decryption on Retrieval
```

## Core Entities

### User Profile
- Basic user information (name, email, phone)
- Encryption key generation
- Profile completion tracking

### Assets
- Digital and physical asset inventory
- Encrypted sensitive data (credentials, account details)
- Asset-beneficiary relationships
- Asset categorization and valuation

### Beneficiaries
- People designated to inherit assets
- Access levels and permissions
- Digital executor designation
- Asset assignment percentages

### Will Document
- Rich text content with embedded asset/beneficiary blocks
- Version control and auto-save
- Publishing and sharing capabilities

### Trigger Settings
- Inactivity monitoring
- Verification methods
- Trusted contacts
- Automated execution rules

## Security Architecture

### Data Protection
- **Client-side encryption** for sensitive asset data
- **User-specific encryption keys** derived from user ID
- **Row Level Security (RLS)** in Supabase
- **Secure password requirements** with validation

### Authentication
- **Supabase Auth** with email/password
- **JWT tokens** for session management
- **Protected routes** with middleware
- **Automatic profile creation** on signup

### Data Access
- **User isolation** through RLS policies
- **Encrypted storage** for sensitive information
- **Audit logging** for security events
- **Secure API endpoints** with authentication

## Key Design Patterns

### 1. Composition API Pattern
All Vue components use the Composition API for better TypeScript support and code organization.

### 2. Store-First Architecture
Business logic is centralized in Pinia stores, with components acting as presentation layers.

### 3. Modal-Based Editing
User interactions use modals instead of page navigation for better UX and state management.

### 4. Type-Safe Development
Comprehensive TypeScript interfaces ensure type safety across the application.

### 5. Reactive Data Flow
Vue's reactivity system ensures UI updates automatically when data changes.

## Performance Considerations

### Client-Side
- **Code splitting** with Nuxt's automatic optimization
- **Lazy loading** of components and routes
- **Efficient state management** with Pinia
- **Optimized bundle size** with tree-shaking

### Server-Side
- **Edge functions** for email processing
- **Database indexing** for query optimization
- **Connection pooling** through Supabase
- **CDN delivery** for static assets

## Development Workflow

### 1. New Developer Onboarding
1. Start with `types/index.ts` to understand data structures
2. Review `stores/` to understand business logic
3. Examine `pages/dashboard/` for main application flow
4. Study `components/editor/` for complex interactions

### 2. Feature Development
1. Define types in `types/index.ts`
2. Implement store actions and getters
3. Create/update database migrations
4. Build UI components
5. Add validation schemas
6. Write tests and documentation

### 3. Common Patterns
- Use composables for reusable logic
- Implement proper error handling
- Follow TypeScript strict mode
- Use Zod for runtime validation
- Encrypt sensitive data before storage

## Deployment Architecture

### Production Environment
- **Vercel/Netlify** for frontend hosting
- **Supabase Cloud** for backend services
- **Custom domain** with SSL certificates
- **Environment variables** for configuration

### Development Environment
- **Local Nuxt dev server** with hot reload
- **Supabase local development** (optional)
- **Environment-specific configurations**
- **Development database** for testing

This architecture provides a solid foundation for a secure, scalable digital will application with modern development practices and user experience considerations.
