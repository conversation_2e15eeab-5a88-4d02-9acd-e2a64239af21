# Pages Documentation

This document covers the main pages and routing structure of the Volnt application.

## Route Structure

```
/                           # Landing page
/auth/
  ├── login                 # User login
  ├── register              # User registration
  ├── register-with-email   # Registration with email demo
  └── email-preview         # Email template preview
/dashboard/                 # Protected dashboard area
  ├── index                 # Dashboard overview
  ├── will                  # Will editor
  ├── assets/
  │   ├── index             # Asset management
  │   ├── new               # Create new asset
  │   ├── [id]/
  │   │   ├── index         # Asset details
  │   │   └── beneficiaries # Asset beneficiary management
  │   └── edit/[id]         # Edit asset (legacy)
  ├── beneficiaries/
  │   ├── index             # Beneficiary management
  │   ├── new               # Create new beneficiary
  │   ├── [id]/
  │   │   └── assets        # Beneficiary asset assignments
  │   └── edit/[id]         # Edit beneficiary
  ├── triggers              # Alert and trigger settings
  ├── profile               # User profile management
  └── settings              # Application settings
```

## Landing Page

### index.vue
**Route**: `/`
**Layout**: `default`

**Purpose**: Marketing landing page introducing Volnt's features.

**Key Sections**:
- Hero section with value proposition
- Feature highlights with icons
- Call-to-action buttons
- Responsive design for all devices

**Navigation**:
- Register button → `/auth/register`
- Login link → `/auth/login`
- About link → `/about`

## Authentication Pages

### auth/login.vue
**Route**: `/auth/login`
**Layout**: `auth`

**Purpose**: User authentication with email/password.

**Form Fields**:
- Email (required)
- Password (required)
- Remember me checkbox

**Features**:
- Form validation with error display
- Loading states during authentication
- Redirect to dashboard on success
- Password visibility toggle

**Integration**:
- Supabase authentication
- Automatic redirect for authenticated users

### auth/register.vue
**Route**: `/auth/register`
**Layout**: `auth`

**Purpose**: New user registration with profile creation.

**Form Fields**:
- First name (required)
- Last name (required)
- Email (required)
- Password (required, with validation)
- Terms agreement checkbox

**Password Requirements**:
- Minimum 8 characters
- At least one uppercase letter
- At least one number
- Real-time validation feedback

**Features**:
- Zod schema validation
- Password strength indicator
- Automatic profile creation
- Terms and conditions agreement

## Dashboard Pages

### dashboard/index.vue
**Route**: `/dashboard`
**Layout**: `dashboard`

**Purpose**: Main dashboard overview with progress tracking and quick actions.

**Key Components**:
- Welcome message with user name
- Progress cards for main tasks:
  - Profile completion
  - Asset management
  - Beneficiary setup
  - Will creation
- Quick action buttons
- Recent activity summary

**Data Sources**:
- User profile from `userStore`
- Asset count from `assetStore`
- Beneficiary count from `beneficiaryStore`
- Will status from `willStore`

**Interactive Elements**:
- Completion checkboxes with local storage
- Progress percentage calculations
- Navigation to specific sections

### dashboard/will.vue
**Route**: `/dashboard/will`
**Layout**: `dashboard`

**Purpose**: Rich text editor for creating and editing will documents.

**Key Features**:
- Enhanced will editor with custom blocks
- Auto-save functionality
- Word count and status indicators
- Asset and beneficiary insertion
- Publish/unpublish controls

**Components Used**:
- `EnhancedWillEditor` - Main editing interface
- `CreateAssetModal` - For creating assets while editing
- `CreateBeneficiaryModal` - For creating beneficiaries while editing

**State Management**:
- Will content stored in `willStore`
- Real-time saving with debouncing
- Change tracking and unsaved changes warning

### dashboard/assets/index.vue
**Route**: `/dashboard/assets`
**Layout**: `dashboard`

**Purpose**: Asset management with filtering, searching, and bulk operations.

**Key Features**:
- Asset grid with card-based display
- Filtering by asset type (All, Digital, Financial, Physical)
- Search functionality
- Sorting options
- Asset allocation summary

**Components Used**:
- `AssetCard` - Individual asset display
- `AssetBeneficiaryModal` - Quick beneficiary assignment
- `EditAssetModal` - In-place editing

**Actions Available**:
- Create new asset
- Edit asset details
- Delete assets
- Assign beneficiaries
- Manage beneficiary relationships
- View asset details

**Data Flow**:
```
Page Load → Fetch Assets → Display Grid → User Actions → Update Store → Refresh Display
```

### dashboard/beneficiaries/index.vue
**Route**: `/dashboard/beneficiaries`
**Layout**: `dashboard`

**Purpose**: Beneficiary management with relationship tracking and digital executor designation.

**Key Features**:
- Beneficiary cards with avatar initials
- Digital executor highlighting
- Asset assignment summary
- Relationship categorization
- Access level indicators

**Components Used**:
- Custom beneficiary cards
- `EditBeneficiaryModal` - In-place editing
- Dropdown action menus

**Special Features**:
- Digital executor management (crown icon, special styling)
- Asset assignment counts
- Contact information display
- Relationship-based grouping

### dashboard/assets/[id]/beneficiaries.vue
**Route**: `/dashboard/assets/[id]/beneficiaries`
**Layout**: `dashboard`

**Purpose**: Detailed beneficiary assignment management for a specific asset.

**Key Features**:
- Asset header with type and value
- Current beneficiary assignments with percentages
- Add/remove beneficiary assignments
- Percentage validation (must total ≤ 100%)
- Real-time percentage updates

**Components Used**:
- `AssetBeneficiaryModal` - Assignment management
- Percentage input controls
- Assignment summary cards

**Validation Rules**:
- Total percentage cannot exceed 100%
- Minimum 1% per assignment
- Automatic percentage suggestions

### dashboard/beneficiaries/[id]/assets.vue
**Route**: `/dashboard/beneficiaries/[id]/assets`
**Layout**: `dashboard`

**Purpose**: Asset assignment management from beneficiary perspective.

**Key Features**:
- Beneficiary profile header
- Assigned assets with percentages
- Asset type categorization
- Total inheritance value calculation
- Assignment modification controls

## Page Patterns

### 1. Layout Usage
All dashboard pages use the `dashboard` layout:

```vue
<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
});
</script>
```

### 2. Store Integration
Pages typically integrate multiple stores:

```vue
<script setup lang="ts">
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();
const userStore = useUserStore();

// Fetch data on mount
onMounted(async () => {
  await Promise.all([
    assetStore.fetchAssets(),
    beneficiaryStore.fetchBeneficiaries(),
    userStore.fetchProfile()
  ]);
});
</script>
```

### 3. Loading States
Pages implement consistent loading patterns:

```vue
<template>
  <div v-if="isLoading" class="loading-spinner">
    Loading...
  </div>
  <div v-else>
    <!-- Page content -->
  </div>
</template>

<script setup lang="ts">
const isLoading = computed(() => 
  assetStore.isLoading || 
  beneficiaryStore.isLoading
);
</script>
```

### 4. Error Handling
Pages handle errors gracefully:

```vue
<script setup lang="ts">
const errorMessage = ref('');

const handleAction = async () => {
  try {
    await someAsyncAction();
  } catch (error) {
    errorMessage.value = error.message;
  }
};
</script>
```

## Navigation Flow

### 1. User Journey
```
Landing Page → Registration → Dashboard → Asset/Beneficiary Setup → Will Creation
```

### 2. Dashboard Navigation
```
Dashboard Overview → Specific Management Pages → Detail Pages → Back to Overview
```

### 3. Modal Workflows
Many actions use modals instead of page navigation:
- Asset creation/editing
- Beneficiary creation/editing
- Assignment management

## Data Loading Strategy

### 1. Initial Load
Dashboard pages load all necessary data on mount:

```typescript
onMounted(async () => {
  await Promise.all([
    userStore.fetchProfile(),
    assetStore.fetchAssets(),
    beneficiaryStore.fetchBeneficiaries(),
    willStore.fetchWillDocument()
  ]);
});
```

### 2. Reactive Updates
Data automatically updates when stores change:

```typescript
const assets = computed(() => assetStore.assets);
const beneficiaries = computed(() => beneficiaryStore.beneficiaries);
```

### 3. Refresh Strategy
After mutations, related data is refreshed:

```typescript
// After creating/updating
await Promise.all([
  assetStore.fetchAssets(),
  beneficiaryStore.fetchBeneficiaries()
]);
```

## SEO and Meta

### 1. Page Titles
Each page sets appropriate titles:

```vue
<script setup lang="ts">
useHead({
  title: 'Asset Management - Volnt'
});
</script>
```

### 2. Meta Descriptions
Important pages include meta descriptions for SEO.

### 3. Open Graph
Landing and public pages include Open Graph meta tags.

## Performance Considerations

### 1. Code Splitting
Nuxt automatically splits code by route for optimal loading.

### 2. Lazy Loading
Heavy components are lazy-loaded when needed.

### 3. Data Caching
Stores implement caching to avoid unnecessary API calls.

### 4. Responsive Design
All pages are mobile-first and responsive across devices.
