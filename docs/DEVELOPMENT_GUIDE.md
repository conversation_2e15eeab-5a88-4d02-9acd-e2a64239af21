# Development Guide

This guide provides essential information for developers working on the Volnt codebase, including setup, workflows, and best practices.

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm/yarn/pnpm
- Git
- Supabase account (for backend services)

### Initial Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd volnt
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   ```
   
   Configure required environment variables:
   ```env
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   RESEND_API_KEY=your_resend_api_key
   ENCRYPTION_SALT=your_encryption_salt
   POSTHOG_API_KEY=your_posthog_key
   POSTHOG_HOST=your_posthog_host
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## Understanding the Codebase

### Recommended Learning Path

#### 1. Start with Types (`types/index.ts`)
Understanding the data structures is crucial:
- Review `Asset`, `Beneficiary`, `User` interfaces
- Understand enum types (`AssetType`, `AccessLevel`, etc.)
- Study relationships between entities

#### 2. Examine Stores (`stores/`)
Business logic is centralized in Pinia stores:
- **userStore.ts**: User authentication and profile
- **assetStore.ts**: Asset management operations
- **beneficiaryStore.ts**: Beneficiary and assignment management
- **willStore.ts**: Will document operations

#### 3. Study Core Pages (`pages/dashboard/`)
Main application functionality:
- **index.vue**: Dashboard overview and progress tracking
- **will.vue**: Rich text editor for will creation
- **assets/index.vue**: Asset management interface
- **beneficiaries/index.vue**: Beneficiary management interface

#### 4. Review Key Components (`components/`)
Critical UI components:
- **editor/EnhancedWillEditor.vue**: TipTap-based rich text editor
- **editor/CreateAssetModal.vue**: Asset creation interface
- **editor/CreateBeneficiaryModal.vue**: Beneficiary creation interface
- **ui/AssetCard.vue**: Asset display component

#### 5. Understand Database Schema (`supabase/migrations/`)
Database structure and relationships:
- User profiles and authentication
- Asset and beneficiary tables
- Junction tables for relationships
- Trigger and security settings

## Development Workflow

### 1. Feature Development Process

#### Planning Phase
1. Review requirements and acceptance criteria
2. Identify affected components and stores
3. Plan database schema changes if needed
4. Design component interfaces and props

#### Implementation Phase
1. **Database First**: Create/update migrations if needed
2. **Types**: Define or update TypeScript interfaces
3. **Store Logic**: Implement business logic in appropriate stores
4. **Components**: Build or update UI components
5. **Pages**: Integrate components into pages
6. **Validation**: Add form validation with Zod schemas

#### Testing Phase
1. Test all user flows manually
2. Verify data persistence and retrieval
3. Test responsive design on different devices
4. Validate error handling and edge cases

### 2. Code Organization Patterns

#### Component Structure
```vue
<template>
  <!-- Template with semantic HTML -->
</template>

<script setup lang="ts">
// Imports
import { ref, computed, onMounted } from 'vue';
import { useStore } from '~/stores/store';

// Props and emits
const props = defineProps<PropsInterface>();
const emit = defineEmits<EmitsInterface>();

// Store integration
const store = useStore();

// Reactive state
const localState = ref('');

// Computed properties
const computedValue = computed(() => store.someGetter);

// Methods
const handleAction = async () => {
  // Implementation
};

// Lifecycle
onMounted(() => {
  // Initialization
});
</script>
```

#### Store Structure
```typescript
export const useExampleStore = defineStore('example', {
  state: (): StateInterface => ({
    // State properties
  }),
  
  getters: {
    // Computed properties
    derivedValue: (state) => state.value * 2,
    
    // Parameterized getters
    getById: (state) => (id: string) => state.items.find(item => item.id === id)
  },
  
  actions: {
    // Async operations
    async fetchData() {
      this.isLoading = true;
      try {
        // API calls
      } catch (error) {
        // Error handling
      } finally {
        this.isLoading = false;
      }
    }
  }
});
```

### 3. Common Development Tasks

#### Adding a New Asset Type
1. Update `AssetType` enum in `types/index.ts`
2. Add type mapping in relevant components
3. Update asset creation/editing forms
4. Add appropriate icons and styling
5. Test asset creation and management

#### Creating a New Modal Component
1. Create component in `components/editor/` or `components/ui/`
2. Follow modal pattern with consistent props/events
3. Implement form validation
4. Add loading states and error handling
5. Integrate with parent components

#### Adding New Store Actions
1. Define action in appropriate store
2. Implement proper error handling
3. Update local state after API calls
4. Add TypeScript types for parameters
5. Test integration with components

## Best Practices

### 1. TypeScript
- Use strict mode and avoid `any` types
- Define interfaces for all data structures
- Use generic types for reusable patterns
- Implement proper type guards when needed

### 2. Vue 3 Composition API
- Use `ref()` for primitive reactive values
- Use `reactive()` for object reactive values
- Prefer `computed()` over methods for derived state
- Use `watch()` for side effects

### 3. State Management
- Keep business logic in stores, not components
- Use getters for computed store values
- Implement consistent error handling patterns
- Refresh related data after mutations

### 4. Component Design
- Keep components focused and single-purpose
- Use props for data down, events for data up
- Implement proper loading and error states
- Follow consistent naming conventions

### 5. Styling
- Use Tailwind CSS utility classes
- Follow DaisyUI component patterns
- Implement mobile-first responsive design
- Maintain consistent spacing and typography

### 6. Security
- Encrypt sensitive data before storage
- Validate all user inputs
- Use proper authentication checks
- Implement Row Level Security in database

## Testing Strategy

### 1. Manual Testing Checklist
- [ ] User registration and login flow
- [ ] Asset creation, editing, and deletion
- [ ] Beneficiary creation, editing, and deletion
- [ ] Asset-beneficiary assignment management
- [ ] Will editor functionality
- [ ] Responsive design on mobile/tablet/desktop
- [ ] Error handling and validation

### 2. Data Validation Testing
- [ ] Form validation with invalid inputs
- [ ] Percentage validation in assignments
- [ ] Digital executor constraints
- [ ] Required field validation

### 3. Integration Testing
- [ ] Store actions with API calls
- [ ] Component communication via props/events
- [ ] Modal workflows and state management
- [ ] Navigation and routing

## Debugging Tips

### 1. Vue DevTools
- Install Vue DevTools browser extension
- Inspect component state and props
- Monitor Pinia store state changes
- Track event emissions

### 2. Network Debugging
- Use browser DevTools Network tab
- Monitor Supabase API calls
- Check authentication headers
- Verify request/response data

### 3. Console Debugging
- Use `console.log()` strategically
- Check browser console for errors
- Monitor Supabase client logs
- Use Vue's `$log` for component debugging

### 4. Common Issues
- **Authentication**: Check Supabase session state
- **Data not loading**: Verify store actions are called
- **Styling issues**: Check Tailwind class conflicts
- **TypeScript errors**: Ensure proper type definitions

## Performance Optimization

### 1. Code Splitting
- Nuxt automatically splits code by routes
- Use dynamic imports for heavy components
- Lazy load modals and complex components

### 2. Data Management
- Implement proper caching in stores
- Avoid unnecessary API calls
- Use computed properties for derived data
- Debounce user input for search/filter

### 3. Bundle Optimization
- Tree-shake unused dependencies
- Optimize images and assets
- Use appropriate bundle analysis tools
- Monitor bundle size changes

## Deployment

### 1. Build Process
```bash
npm run build
```

### 2. Environment Configuration
- Set production environment variables
- Configure Supabase production instance
- Set up email service credentials
- Configure analytics tracking

### 3. Deployment Platforms
- **Vercel**: Automatic deployments from Git
- **Netlify**: Static site hosting with edge functions
- **Custom**: Docker containerization available

## Contributing Guidelines

### 1. Code Style
- Use Prettier for code formatting
- Follow ESLint rules
- Use conventional commit messages
- Write descriptive variable and function names

### 2. Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Submit PR with clear description
5. Address review feedback
6. Merge after approval

### 3. Documentation
- Update relevant documentation files
- Add JSDoc comments for complex functions
- Include usage examples for new components
- Maintain README and setup instructions

This development guide provides the foundation for effective contribution to the Volnt codebase. For specific implementation details, refer to the individual documentation files for stores, components, pages, and types.
