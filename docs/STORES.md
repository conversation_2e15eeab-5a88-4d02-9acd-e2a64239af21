# Pinia Stores Documentation

This document covers all Pinia stores used in the Volnt application for state management.

## Store Overview

The application uses four main stores:
- **userStore**: User profile and authentication state
- **assetStore**: Asset management and operations
- **beneficiaryStore**: Beneficiary management and assignments
- **willStore**: Will document content and operations

## userStore.ts

### Purpose
Manages user profile information, authentication state, and encryption keys.

### State Structure
```typescript
interface UserState {
  isLoading: boolean;
  profile: {
    firstName: string;
    lastName: string;
    phoneNumber: string | null;
  } | null;
  encryptionKey: string | null;
}
```

### Key Getters
- **fullName**: Computed full name from first and last name
- **isProfileComplete**: Checks if required profile fields are filled

### Main Actions

#### `fetchProfile()`
Retrieves user profile from Supabase and generates encryption key.
```typescript
await userStore.fetchProfile();
```

#### `updateProfile(profileData)`
Updates user profile information.
```typescript
const result = await userStore.updateProfile({
  firstName: '<PERSON>',
  lastName: 'Do<PERSON>',
  phoneNumber: '+1234567890'
});
```

#### `clearUserData()`
Clears all user data from store (used on logout).

### Usage Example
```typescript
const userStore = useUserStore();
const userName = computed(() => userStore.fullName);

// Check if profile is complete
if (!userStore.isProfileComplete) {
  // Redirect to profile completion
}
```

## assetStore.ts

### Purpose
Manages digital and physical assets, including creation, updates, and beneficiary assignments.

### State Structure
```typescript
interface AssetState {
  assets: Asset[];
  isLoading: boolean;
  currentAsset: Asset | null;
}
```

### Key Getters
- **digitalAssets**: Filters assets by digital types (DIGITAL_ACCOUNT, CRYPTOCURRENCY, SOCIAL_MEDIA)
- **financialAssets**: Filters assets by financial types (FINANCIAL_ACCOUNT, INSURANCE)
- **physicalAssets**: Filters assets by physical types (REAL_ESTATE, VEHICLE, etc.)
- **totalAssetValue**: Sum of all asset values
- **assignedAssets**: Assets that have beneficiaries assigned
- **getAssetById**: Function to find asset by ID

### Main Actions

#### `fetchAssets()`
Retrieves all user assets with beneficiary assignments.
```typescript
await assetStore.fetchAssets();
```

#### `createAsset(asset, sensitiveData?)`
Creates a new asset with optional encrypted sensitive data.
```typescript
const newAsset = await assetStore.createAsset({
  name: 'Bitcoin Wallet',
  type: 'CRYPTOCURRENCY',
  value: 50000,
  currency: 'USD'
}, {
  accountDetails: { username: 'user123' },
  credentials: { password: 'secret' }
});
```

#### `updateAsset(assetId, assetData, sensitiveData?)`
Updates existing asset information.
```typescript
await assetStore.updateAsset(assetId, {
  name: 'Updated Asset Name',
  value: 75000
});
```

#### `deleteAsset(assetId)`
Deletes an asset and all related data.
```typescript
const success = await assetStore.deleteAsset(assetId);
```

### Usage Example
```typescript
const assetStore = useAssetStore();

// Get categorized assets
const digitalAssets = computed(() => assetStore.digitalAssets);
const totalValue = computed(() => assetStore.totalAssetValue);

// Find specific asset
const asset = assetStore.getAssetById('asset-id');
```

## beneficiaryStore.ts

### Purpose
Manages beneficiaries, their relationships to assets, and digital executor designation.

### State Structure
```typescript
interface BeneficiaryState {
  beneficiaries: Beneficiary[];
  isLoading: boolean;
  currentBeneficiary: Beneficiary | null;
}
```

### Key Getters
- **primaryBeneficiaries**: Filters by primary relationships (Spouse, Child, Parent)
- **otherBeneficiaries**: All other beneficiaries
- **totalBeneficiaries**: Count of all beneficiaries
- **assignedBeneficiaries**: Beneficiaries with asset assignments
- **digitalExecutor**: The designated digital executor
- **getBeneficiaryById**: Function to find beneficiary by ID

### Main Actions

#### `fetchBeneficiaries()`
Retrieves all beneficiaries with their asset assignments.
```typescript
await beneficiaryStore.fetchBeneficiaries();
```

#### `createBeneficiary(beneficiary)`
Creates a new beneficiary.
```typescript
const newBeneficiary = await beneficiaryStore.createBeneficiary({
  name: 'John Doe',
  email: '<EMAIL>',
  relationship: 'Spouse',
  accessLevel: 'FULL',
  notificationPreference: 'EMAIL',
  isDigitalExecutor: true
});
```

#### `updateBeneficiary(beneficiaryId, beneficiaryData)`
Updates beneficiary information.
```typescript
await beneficiaryStore.updateBeneficiary(beneficiaryId, {
  name: 'Updated Name',
  accessLevel: 'LIMITED'
});
```

#### `assignAsset(beneficiaryId, assetId, percentage)`
Assigns an asset to a beneficiary with a percentage.
```typescript
await beneficiaryStore.assignAsset(beneficiaryId, assetId, 50);
```

#### `removeAssetAssignment(beneficiaryId, assetId)`
Removes an asset assignment from a beneficiary.
```typescript
await beneficiaryStore.removeAssetAssignment(beneficiaryId, assetId);
```

#### `setDigitalExecutor(beneficiaryId)` / `removeDigitalExecutor(beneficiaryId)`
Manages digital executor designation.
```typescript
await beneficiaryStore.setDigitalExecutor(beneficiaryId);
```

### Usage Example
```typescript
const beneficiaryStore = useBeneficiaryStore();

// Get digital executor
const executor = computed(() => beneficiaryStore.digitalExecutor);

// Get beneficiaries by category
const family = computed(() => beneficiaryStore.primaryBeneficiaries);
const others = computed(() => beneficiaryStore.otherBeneficiaries);
```

## willStore.ts

### Purpose
Manages will document content, auto-save functionality, and publishing state.

### State Structure
```typescript
interface WillState {
  willDocument: WillDocument | null;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
}
```

### Key Getters
- **hasContent**: Checks if will has meaningful content
- **isPublished**: Whether the will is published
- **wordCount**: Counts words in the will content (excluding HTML)

### Main Actions

#### `fetchWillDocument()`
Retrieves the user's will document.
```typescript
await willStore.fetchWillDocument();
```

#### `saveWillDocument(title?, content?)`
Saves will document with optional title and content updates.
```typescript
await willStore.saveWillDocument('My Will', '<p>Will content...</p>');
```

#### `publishWill()`
Publishes the will document.
```typescript
await willStore.publishWill();
```

#### `unpublishWill()`
Unpublishes the will document.
```typescript
await willStore.unpublishWill();
```

### Usage Example
```typescript
const willStore = useWillStore();

// Check will status
const hasContent = computed(() => willStore.hasContent);
const wordCount = computed(() => willStore.wordCount);

// Auto-save functionality
watch(willContent, async (newContent) => {
  await willStore.saveWillDocument(undefined, newContent);
});
```

## Store Integration Patterns

### 1. Cross-Store Communication
Stores often work together for complex operations:

```typescript
// Creating an asset and assigning beneficiaries
const asset = await assetStore.createAsset(assetData);
if (asset) {
  for (const beneficiaryId of selectedBeneficiaries) {
    await beneficiaryStore.assignAsset(beneficiaryId, asset.id, 100);
  }
  // Refresh both stores
  await Promise.all([
    assetStore.fetchAssets(),
    beneficiaryStore.fetchBeneficiaries()
  ]);
}
```

### 2. Loading State Management
All stores implement consistent loading patterns:

```typescript
// In components
const isLoading = computed(() => 
  assetStore.isLoading || 
  beneficiaryStore.isLoading || 
  willStore.isLoading
);
```

### 3. Error Handling
Stores return consistent error responses:

```typescript
const result = await userStore.updateProfile(data);
if (result.error) {
  // Handle error
  showError(result.error);
} else {
  // Handle success
  showSuccess('Profile updated');
}
```

## Best Practices

### 1. Always Fetch Fresh Data
When modifying data, refresh related stores:

```typescript
// After creating/updating/deleting
await Promise.all([
  assetStore.fetchAssets(),
  beneficiaryStore.fetchBeneficiaries()
]);
```

### 2. Use Getters for Computed Data
Prefer getters over computing in components:

```typescript
// Good: Use store getter
const digitalAssets = computed(() => assetStore.digitalAssets);

// Avoid: Computing in component
const digitalAssets = computed(() => 
  assetStore.assets.filter(a => digitalTypes.includes(a.type))
);
```

### 3. Handle Loading States
Always show loading indicators during async operations:

```typescript
const isLoading = ref(false);

const handleAction = async () => {
  isLoading.value = true;
  try {
    await store.someAction();
  } finally {
    isLoading.value = false;
  }
};
```

### 4. Type Safety
Use TypeScript interfaces for all store operations:

```typescript
// Define proper types
interface CreateAssetData {
  name: string;
  type: AssetType;
  value?: number;
}

// Use in store actions
async createAsset(data: CreateAssetData) {
  // Implementation
}
```
