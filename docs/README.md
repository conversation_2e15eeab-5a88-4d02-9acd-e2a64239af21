# Volnt Documentation

Comprehensive documentation for the Volnt digital will and testament application.

## Overview

Volnt is a modern web application built with Nuxt 3, Vue 3, and Supabase that allows users to create secure digital wills, manage their digital assets, and designate beneficiaries for their estate planning needs.

## Documentation Structure

This documentation is organized into focused sections for different aspects of the codebase:

### 📋 [Architecture Overview](./ARCHITECTURE.md)
- Project structure and technology stack
- Data flow and security architecture
- Core design patterns and principles
- Deployment and performance considerations

### 🏪 [Stores Documentation](./STORES.md)
- Pinia store structure and usage
- State management patterns
- API integration and data flow
- Store actions, getters, and best practices

### 🧩 [Components Documentation](./COMPONENTS.md)
- Core component library and usage
- Props, events, and component patterns
- Modal components and UI elements
- Component composition and relationships

### 📄 [Pages Documentation](./PAGES.md)
- Route structure and navigation flow
- Page-specific functionality and features
- Data loading and state management
- User interaction patterns

### 🔧 [TypeScript Types](./TYPES.md)
- Core entity interfaces and relationships
- Enum definitions and utility types
- Store state types and component props
- Type safety patterns and best practices

### 🚀 [Development Guide](./DEVELOPMENT_GUIDE.md)
- Setup and onboarding for new developers
- Development workflow and best practices
- Testing strategies and debugging tips
- Contributing guidelines and code standards

## Quick Start for Developers

### 1. First-Time Setup
```bash
# Clone and install
git clone <repository-url>
cd volnt
npm install

# Configure environment
cp .env.example .env
# Edit .env with your Supabase and service credentials

# Start development
npm run dev
```

### 2. Understanding the Codebase
For new developers, follow this recommended learning path:

1. **Start with [Types](./TYPES.md)** - Understand the data structures
2. **Review [Stores](./STORES.md)** - Learn the business logic layer
3. **Study [Pages](./PAGES.md)** - See how the application flows
4. **Examine [Components](./COMPONENTS.md)** - Understand the UI building blocks
5. **Read [Architecture](./ARCHITECTURE.md)** - Get the big picture

### 3. Key Files to Understand
- `types/index.ts` - All TypeScript interfaces
- `stores/` - Business logic and state management
- `pages/dashboard/` - Main application functionality
- `components/editor/` - Rich text editor and modals
- `supabase/migrations/` - Database schema

## Application Features

### Core Functionality
- **User Authentication**: Secure registration and login with Supabase
- **Asset Management**: Digital and physical asset inventory with encryption
- **Beneficiary Management**: Designate inheritors with access controls
- **Will Creation**: Rich text editor with embedded asset/beneficiary blocks
- **Assignment Management**: Percentage-based asset distribution
- **Digital Executor**: Designate someone to execute the digital will
- **Trigger System**: Automated will execution based on inactivity

### Technical Highlights
- **Security**: Client-side encryption for sensitive data
- **Real-time**: Live updates with Supabase subscriptions
- **Responsive**: Mobile-first design with Tailwind CSS
- **Type-safe**: Full TypeScript implementation
- **Modern**: Vue 3 Composition API with Nuxt 3

## Technology Stack

### Frontend
- **Nuxt 3**: Full-stack Vue.js framework
- **Vue 3**: Progressive JavaScript framework
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **DaisyUI**: Component library
- **TipTap**: Rich text editor

### Backend
- **Supabase**: Backend-as-a-Service
- **PostgreSQL**: Database with Row Level Security
- **Edge Functions**: Serverless functions for email
- **Resend**: Email delivery service

### State Management
- **Pinia**: Vue state management
- **Reactive**: Vue 3 reactivity system
- **Computed**: Derived state management

## Security Features

- **Encryption**: Client-side encryption for sensitive asset data
- **Authentication**: Supabase Auth with JWT tokens
- **Authorization**: Row Level Security policies
- **Validation**: Runtime validation with Zod schemas
- **Audit Trail**: Security event logging

## Development Workflow

### Feature Development
1. Plan database schema changes
2. Define TypeScript interfaces
3. Implement store logic
4. Build UI components
5. Create/update pages
6. Add validation and testing

### Code Organization
- **Stores**: Business logic and API integration
- **Components**: Reusable UI elements
- **Pages**: Route-based application views
- **Utils**: Helper functions and utilities
- **Types**: TypeScript definitions

## Contributing

### Code Standards
- TypeScript strict mode
- Vue 3 Composition API
- Tailwind CSS for styling
- Conventional commit messages
- ESLint and Prettier formatting

### Pull Request Process
1. Create feature branch
2. Implement with proper types
3. Update documentation
4. Test thoroughly
5. Submit PR with description

## Support and Resources

### Documentation Files
- [Architecture Overview](./ARCHITECTURE.md) - System design and patterns
- [Stores Documentation](./STORES.md) - State management guide
- [Components Documentation](./COMPONENTS.md) - UI component library
- [Pages Documentation](./PAGES.md) - Application pages and routing
- [TypeScript Types](./TYPES.md) - Type definitions and interfaces
- [Development Guide](./DEVELOPMENT_GUIDE.md) - Setup and workflow

### Additional Resources
- [Edit Modals Guide](./EDIT_MODALS_GUIDE.md) - Modal editing functionality
- [Iconify Migration Summary](./ICONIFY_MIGRATION_SUMMARY.md) - Icon system changes
- [Beneficiary Assignment Guide](./BENEFICIARY_ASSIGNMENT_GUIDE.md) - Assignment patterns

### External Documentation
- [Nuxt 3 Documentation](https://nuxt.com/docs)
- [Vue 3 Documentation](https://vuejs.org/guide/)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TipTap Documentation](https://tiptap.dev/docs)

## License

This project is proprietary software. All rights reserved.

---

For specific implementation details, please refer to the individual documentation files linked above. Each file provides in-depth coverage of its respective area of the codebase.
