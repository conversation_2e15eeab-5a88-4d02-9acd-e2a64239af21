import { defineStore } from 'pinia';
import { useSupabaseClient } from '#imports';
import type { WillDocument } from '~/types';

interface WillState {
  willDocument: WillDocument | null;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
}

export const useWillStore = defineStore('will', {
  state: (): WillState => ({
    willDocument: null,
    isLoading: false,
    isSaving: false,
    lastSaved: null
  }),
  
  getters: {
    hasContent: (state) => {
      return state.willDocument?.content && state.willDocument.content.trim().length > 0;
    },
    
    isPublished: (state) => {
      return state.willDocument?.isPublished || false;
    },
    
    wordCount: (state) => {
      if (!state.willDocument?.content) return 0;
      // Remove HTML tags and count words
      const text = state.willDocument.content.replace(/<[^>]*>/g, '');
      return text.trim().split(/\s+/).length;
    }
  },
  
  actions: {
    async fetchWillDocument() {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');
        
        const { data, error } = await supabase
          .from('will_documents')
          .select('*')
          .eq('user_id', user.id)
          .single();
        
        if (error && error.code !== 'PGRST116') throw error;
        
        if (data) {
          this.willDocument = {
            id: data.id,
            userId: data.user_id,
            title: data.title,
            content: data.content || '',
            lastEdited: data.updated_at,
            isPublished: data.is_published,
            publishedAt: data.published_at,
            createdAt: data.created_at,
            updatedAt: data.updated_at
          };
        } else {
          // Create default will document if none exists
          await this.createWillDocument();
        }
        
      } catch (error) {
        console.error('Error fetching will document:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    async createWillDocument() {
      const supabase = useSupabaseClient();
      
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');
        
        const { data, error } = await supabase
          .from('will_documents')
          .insert({
            user_id: user.id,
            title: 'My Digital Will & Testament',
            content: `
<h1>Last Will and Testament</h1>
<p>I, [Your Full Legal Name], residing at [Your Address], being of sound mind, declare this to be my Will.</p>

<h2>Article I - Revocation</h2>
<p>I hereby revoke all former wills and testamentary dispositions previously made by me.</p>

<h2>Article II - Digital Assets</h2>
<p>I own various digital assets which shall be distributed as specified in this document. Access information and detailed instructions are provided below.</p>

<p><em>Use the Insert menu above to add your assets, beneficiaries, and detailed instructions.</em></p>
            `.trim()
          })
          .select()
          .single();
        
        if (error) throw error;
        
        this.willDocument = {
          id: data.id,
          userId: data.user_id,
          title: data.title,
          content: data.content,
          lastEdited: data.updated_at,
          isPublished: data.is_published,
          publishedAt: data.published_at,
          createdAt: data.created_at,
          updatedAt: data.updated_at
        };
        
        return this.willDocument;
        
      } catch (error) {
        console.error('Error creating will document:', error);
        return null;
      }
    },
    
    async saveWillDocument(title?: string, content?: string) {
      if (!this.willDocument) return false;
      
      const supabase = useSupabaseClient();
      this.isSaving = true;
      
      try {
        const updateData: any = {
          updated_at: new Date().toISOString()
        };
        
        if (title !== undefined) {
          updateData.title = title;
        }
        
        if (content !== undefined) {
          updateData.content = content;
        }
        
        const { data, error } = await supabase
          .from('will_documents')
          .update(updateData)
          .eq('id', this.willDocument.id)
          .select()
          .single();
        
        if (error) throw error;
        
        // Update local state
        this.willDocument = {
          ...this.willDocument,
          title: data.title,
          content: data.content,
          lastEdited: data.updated_at,
          updatedAt: data.updated_at
        };
        
        this.lastSaved = new Date();
        return true;
        
      } catch (error) {
        console.error('Error saving will document:', error);
        return false;
      } finally {
        this.isSaving = false;
      }
    },
    
    async publishWillDocument() {
      if (!this.willDocument) return false;
      
      const supabase = useSupabaseClient();
      this.isSaving = true;
      
      try {
        const { data, error } = await supabase
          .from('will_documents')
          .update({
            is_published: true,
            published_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', this.willDocument.id)
          .select()
          .single();
        
        if (error) throw error;
        
        // Update local state
        this.willDocument = {
          ...this.willDocument,
          isPublished: data.is_published,
          publishedAt: data.published_at,
          updatedAt: data.updated_at
        };
        
        return true;
        
      } catch (error) {
        console.error('Error publishing will document:', error);
        return false;
      } finally {
        this.isSaving = false;
      }
    },
    
    async unpublishWillDocument() {
      if (!this.willDocument) return false;
      
      const supabase = useSupabaseClient();
      this.isSaving = true;
      
      try {
        const { data, error } = await supabase
          .from('will_documents')
          .update({
            is_published: false,
            published_at: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', this.willDocument.id)
          .select()
          .single();
        
        if (error) throw error;
        
        // Update local state
        this.willDocument = {
          ...this.willDocument,
          isPublished: data.is_published,
          publishedAt: data.published_at,
          updatedAt: data.updated_at
        };
        
        return true;
        
      } catch (error) {
        console.error('Error unpublishing will document:', error);
        return false;
      } finally {
        this.isSaving = false;
      }
    }
  }
});