import { defineStore } from 'pinia';
import { useSupabaseClient } from '#imports';
import { encryptData, decryptData } from '~/utils/encryption';
import type { Asset, AssetType } from '~/types';
import { useUserStore } from '~/stores/userStore';

interface AssetState {
  assets: Asset[];
  isLoading: boolean;
  currentAsset: Asset | null;
}

export const useAssetStore = defineStore('asset', {
  state: (): AssetState => ({
    assets: [],
    isLoading: false,
    currentAsset: null
  }),
  
  getters: {
    digitalAssets: (state) => {
      const digitalTypes: AssetType[] = ['DIGITAL_ACCOUNT', 'CRYPTOCURRENCY', 'SOCIAL_MEDIA'];
      return state.assets.filter(asset => digitalTypes.includes(asset.type as AssetType));
    },
    
    financialAssets: (state) => {
      const financialTypes: AssetType[] = ['FINANCIAL_ACCOUNT', 'INSURANCE'];
      return state.assets.filter(asset => financialTypes.includes(asset.type as AssetType));
    },
    
    physicalAssets: (state) => {
      const physicalTypes: AssetType[] = ['REAL_ESTATE', 'VEHICLE', 'COLLECTIBLE', 'PERSONAL_BELONGING', 'INTELLECTUAL_PROPERTY'];
      return state.assets.filter(asset => physicalTypes.includes(asset.type as AssetType));
    },
    
    totalAssetValue: (state) => {
      return state.assets.reduce((sum, asset) => sum + (asset.value || 0), 0);
    },
    
    assignedAssets: (state) => {
      return state.assets.filter(asset => asset.beneficiaries && asset.beneficiaries.length > 0);
    },

    getAssetById: (state) => {
      return (id: string) => state.assets.find(asset => asset.id === id);
    }
  },
  
  actions: {
    async fetchAssets() {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        // Fetch assets
        const { data: assetData, error: assetError } = await supabase
          .from('assets')
          .select('*');
        
        if (assetError) throw assetError;
        
        // Fetch asset beneficiaries
        const { data: beneficiaryData, error: beneficiaryError } = await supabase
          .from('asset_beneficiaries')
          .select('*');
        
        if (beneficiaryError) throw beneficiaryError;
        
        // Map beneficiaries to assets
        this.assets = assetData.map((asset: any) => {
          const assetBeneficiaries = beneficiaryData
            .filter((b: any) => b.asset_id === asset.id)
            .map((b: any) => ({
              id: b.beneficiary_id,
              assetId: b.asset_id,
              percentage: b.percentage
            }));
          
          return {
            id: asset.id,
            userId: asset.user_id,
            type: asset.type,
            name: asset.name,
            description: asset.description,
            value: asset.value,
            currency: asset.currency,
            location: asset.location,
            beneficiaries: assetBeneficiaries,
            createdAt: asset.created_at,
            updatedAt: asset.updated_at
          };
        });
        
      } catch (error) {
        console.error('Error fetching assets:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    async fetchAssetDetails(assetId: string) {
      const supabase = useSupabaseClient();
      const userStore = useUserStore();
      
      if (!userStore.encryptionKey) {
        await userStore.fetchProfile();
      }
      
      this.isLoading = true;
      
      try {
        // Fetch asset
        const { data: asset, error: assetError } = await supabase
          .from('assets')
          .select('*')
          .eq('id', assetId)
          .single();
        
        if (assetError) throw assetError;
        
        // Fetch encrypted details
        const { data: detailsData, error: detailsError } = await supabase
          .from('asset_details')
          .select('*')
          .eq('asset_id', assetId);
        
        if (detailsError) throw detailsError;
        
        // Decrypt sensitive data
        const accountDetails: any = {};
        const credentials: any = {};
        
        if (detailsData.length > 0 && userStore.encryptionKey) {
          for (const detail of detailsData) {
            try {
              const decryptedValue = decryptData(
                detail.encrypted_value,
                detail.iv,
                userStore.encryptionKey
              );
              
              if (detail.key_name.startsWith('account_')) {
                accountDetails[detail.key_name.replace('account_', '')] = decryptedValue;
              } else if (detail.key_name.startsWith('credential_')) {
                credentials[detail.key_name.replace('credential_', '')] = decryptedValue;
              }
            } catch (error) {
              console.error('Error decrypting data:', error);
            }
          }
        }
        
        // Fetch beneficiaries
        const { data: beneficiaryData, error: beneficiaryError } = await supabase
          .from('asset_beneficiaries')
          .select('*')
          .eq('asset_id', assetId);
        
        if (beneficiaryError) throw beneficiaryError;
        
        const beneficiaries = beneficiaryData.map((b: any) => ({
          id: b.beneficiary_id,
          assetId: b.asset_id,
          percentage: b.percentage
        }));
        
        // Set current asset with all details
        this.currentAsset = {
          id: asset.id,
          userId: asset.user_id,
          type: asset.type,
          name: asset.name,
          description: asset.description,
          value: asset.value,
          currency: asset.currency,
          location: asset.location,
          accountDetails: Object.keys(accountDetails).length > 0 ? accountDetails : undefined,
          credentials: Object.keys(credentials).length > 0 ? credentials : undefined,
          beneficiaries: beneficiaries,
          createdAt: asset.created_at,
          updatedAt: asset.updated_at
        };
        
        return this.currentAsset;
      } catch (error) {
        console.error('Error fetching asset details:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async createAsset(asset: Partial<Asset>, sensitiveData?: any) {
      const supabase = useSupabaseClient();
      const userStore = useUserStore();
      
      if (!userStore.encryptionKey) {
        await userStore.fetchProfile();
      }
      
      this.isLoading = true;
      
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');
        
        // Insert asset
        const { data: newAsset, error } = await supabase
          .from('assets')
          .insert({
            user_id: user.id,
            type: asset.type,
            name: asset.name,
            description: asset.description,
            value: asset.value,
            currency: asset.currency,
            location: asset.location
          })
          .select()
          .single();
        
        if (error) throw error;
        
        // Encrypt and store sensitive data if provided
        if (sensitiveData && userStore.encryptionKey) {
          const detailsToInsert = [];
          
          // Handle account details
          if (sensitiveData.accountDetails) {
            for (const [key, value] of Object.entries(sensitiveData.accountDetails)) {
              if (value) {
                const encrypted = encryptData(value as string, userStore.encryptionKey);
                detailsToInsert.push({
                  asset_id: newAsset.id,
                  key_name: `account_${key}`,
                  encrypted_value: encrypted.encryptedData,
                  iv: encrypted.iv
                });
              }
            }
          }
          
          // Handle credentials
          if (sensitiveData.credentials) {
            for (const [key, value] of Object.entries(sensitiveData.credentials)) {
              if (value) {
                const encrypted = encryptData(value as string, userStore.encryptionKey);
                detailsToInsert.push({
                  asset_id: newAsset.id,
                  key_name: `credential_${key}`,
                  encrypted_value: encrypted.encryptedData,
                  iv: encrypted.iv
                });
              }
            }
          }
          
          if (detailsToInsert.length > 0) {
            const { error: detailsError } = await supabase
              .from('asset_details')
              .insert(detailsToInsert);
            
            if (detailsError) throw detailsError;
          }
        }
        
        // Add the new asset to the store
        const formattedAsset: Asset = {
          id: newAsset.id,
          userId: newAsset.user_id,
          type: newAsset.type as AssetType,
          name: newAsset.name,
          description: newAsset.description,
          value: newAsset.value,
          currency: newAsset.currency,
          location: newAsset.location,
          accountDetails: sensitiveData?.accountDetails,
          credentials: sensitiveData?.credentials ? { 
            encryptedData: 'ENCRYPTED',
            iv: 'ENCRYPTED' 
          } : undefined,
          beneficiaries: [],
          createdAt: newAsset.created_at,
          updatedAt: newAsset.updated_at
        };
        
        this.assets.push(formattedAsset);
        return formattedAsset;
        
      } catch (error) {
        console.error('Error creating asset:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async updateAsset(assetId: string, assetData: Partial<Asset>, sensitiveData?: any) {
      const supabase = useSupabaseClient();
      const userStore = useUserStore();
      
      this.isLoading = true;
      
      try {
        // Update basic asset data
        const { data: updatedAsset, error: updateError } = await supabase
          .from('assets')
          .update({
            name: assetData.name,
            description: assetData.description,
            value: assetData.value,
            currency: assetData.currency,
            location: assetData.location,
            updated_at: new Date().toISOString()
          })
          .eq('id', assetId)
          .select()
          .single();
        
        if (updateError) throw updateError;
        
        // Update sensitive data if provided
        if (sensitiveData && userStore.encryptionKey) {
          // Delete existing details
          await supabase
            .from('asset_details')
            .delete()
            .eq('asset_id', assetId);
          
          const detailsToInsert = [];
          
          // Handle account details
          if (sensitiveData.accountDetails) {
            for (const [key, value] of Object.entries(sensitiveData.accountDetails)) {
              if (value) {
                const encrypted = encryptData(value as string, userStore.encryptionKey);
                detailsToInsert.push({
                  asset_id: assetId,
                  key_name: `account_${key}`,
                  encrypted_value: encrypted.encryptedData,
                  iv: encrypted.iv
                });
              }
            }
          }
          
          // Handle credentials
          if (sensitiveData.credentials) {
            for (const [key, value] of Object.entries(sensitiveData.credentials)) {
              if (value) {
                const encrypted = encryptData(value as string, userStore.encryptionKey);
                detailsToInsert.push({
                  asset_id: assetId,
                  key_name: `credential_${key}`,
                  encrypted_value: encrypted.encryptedData,
                  iv: encrypted.iv
                });
              }
            }
          }
          
          if (detailsToInsert.length > 0) {
            const { error: detailsError } = await supabase
              .from('asset_details')
              .insert(detailsToInsert);
            
            if (detailsError) throw detailsError;
          }
        }
        
        // Update local state
        const index = this.assets.findIndex(a => a.id === assetId);
        if (index !== -1) {
          this.assets[index] = {
            ...this.assets[index],
            name: updatedAsset.name,
            description: updatedAsset.description,
            value: updatedAsset.value,
            currency: updatedAsset.currency,
            location: updatedAsset.location,
            updatedAt: updatedAsset.updated_at
          };
        }
        
        return updatedAsset;
        
      } catch (error) {
        console.error('Error updating asset:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async deleteAsset(assetId: string) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { error } = await supabase
          .from('assets')
          .delete()
          .eq('id', assetId);
        
        if (error) throw error;
        
        // Remove from local state
        this.assets = this.assets.filter(asset => asset.id !== assetId);
        return true;
        
      } catch (error) {
        console.error('Error deleting asset:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    }
  }
});