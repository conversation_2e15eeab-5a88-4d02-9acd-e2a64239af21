import { defineS<PERSON> } from 'pinia';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import { generateUserKey } from '~/utils/encryption';

interface UserState {
  isLoading: boolean;
  profile: {
    firstName: string;
    lastName: string;
    phoneNumber: string | null;
  } | null;
  encryptionKey: string | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    isLoading: false,
    profile: null,
    encryptionKey: null,
  }),
  
  getters: {
    fullName: (state) => {
      if (!state.profile) return '';
      return `${state.profile.firstName} ${state.profile.lastName}`.trim();
    },
    
    isProfileComplete: (state) => {
      if (!state.profile) return false;
      return !!(state.profile.firstName && state.profile.lastName);
    },
  },
  
  actions: {
    async fetchProfile() {
      const supabase = useSupabaseClient();
      const user = useSupabaseUser();
      
      if (!user.value) return null;
      
      this.isLoading = true;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.value.id)
          .single();
        
        if (error) throw error;
        
        this.profile = {
          firstName: data.first_name || '',
          lastName: data.last_name || '',
          phoneNumber: data.phone_number,
        };
        
        // Generate encryption key based on user ID
        this.encryptionKey = generateUserKey(user.value.id);
        
        return data;
      } catch (error) {
        console.error('Error fetching profile:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async updateProfile(profileData: { firstName: string; lastName: string; phoneNumber?: string }) {
      const supabase = useSupabaseClient();
      const user = useSupabaseUser();
      
      if (!user.value) return { error: 'User not authenticated' };
      
      this.isLoading = true;
      
      try {
        const { error } = await supabase
          .from('profiles')
          .update({
            first_name: profileData.firstName,
            last_name: profileData.lastName,
            phone_number: profileData.phoneNumber,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.value.id);
        
        if (error) throw error;
        
        // Update local state
        this.profile = {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          phoneNumber: profileData.phoneNumber || null,
        };
        
        return { success: true };
      } catch (error: any) {
        console.error('Error updating profile:', error);
        return { error: error.message || 'Failed to update profile' };
      } finally {
        this.isLoading = false;
      }
    },
    
    async clearUserData() {
      this.profile = null;
      this.encryptionKey = null;
    }
  }
});