import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { tool } from 'ai';
import { <PERSON>ca } from "@picahq/ai";
import { z } from 'zod';

// Pica OneTool implementation for legal will generation
const picaOneTool = tool({
  description: "Pica OneTool: A specialized tool for generating legal language for wills based on a given topic and context. Use this when users need specific legal clauses, will sections, or formal legal language.",
  parameters: z.object({
    topic: z.string().describe("The specific topic for which legal language is needed (e.g., digital assets, executor appointment, beneficiary designation, revocation clause)."),
    context: z.string().describe("Additional context or details to refine the legal language generation, including user's specific situation or requirements."),
    jurisdiction: z.string().optional().describe("Legal jurisdiction if specified (defaults to general/universal language)."),
  }),
  execute: async ({ topic, context, jurisdiction = "general" }) => {
    console.log(`Pica OneTool executed for topic: ${topic}, context: ${context}, jurisdiction: ${jurisdiction}`);
    
    // Generate legal language based on the topic
    const legalTemplates = {
      'digital assets': `
        <h3>Article - Digital Assets</h3>
        <p>I own various digital assets including but not limited to email accounts, social media profiles, cryptocurrency wallets, cloud storage accounts, and digital files. I direct that these digital assets be distributed as follows:</p>
        <ul>
          <li>My digital executor shall have the authority to access, manage, and distribute all digital assets according to the specific instructions provided in my Digital Asset Inventory.</li>
          <li>Access credentials and recovery information are stored securely and can be accessed through the procedures outlined in my Digital Access Instructions.</li>
          <li>All digital assets shall be distributed to my designated beneficiaries as specified in the attached Digital Asset Distribution Schedule.</li>
        </ul>
        <p>I authorize my digital executor to take all necessary steps to effectuate the transfer of these digital assets, including but not limited to contacting service providers, providing death certificates, and executing any required documentation.</p>
      `,
      'executor': `
        <h3>Article - Appointment of Executor</h3>
        <p>I hereby appoint [EXECUTOR NAME], currently residing at [EXECUTOR ADDRESS], as the Executor of this Will and of my estate.</p>
        <p>If [EXECUTOR NAME] is unable or unwilling to serve as Executor, I appoint [ALTERNATE EXECUTOR NAME], currently residing at [ALTERNATE EXECUTOR ADDRESS], as the alternate Executor.</p>
        <p>I grant my Executor the following powers and authorities:</p>
        <ul>
          <li>To pay all my debts, funeral expenses, and testamentary expenses;</li>
          <li>To collect, manage, and distribute all assets of my estate;</li>
          <li>To sell, transfer, or otherwise dispose of any property as necessary;</li>
          <li>To represent my estate in all legal proceedings;</li>
          <li>To access and manage all digital accounts and assets;</li>
          <li>To execute all documents necessary for the administration of my estate.</li>
        </ul>
        <p>My Executor shall serve without bond unless required by law.</p>
      `,
      'beneficiary': `
        <h3>Article - Beneficiary Designations</h3>
        <p>I give, devise, and bequeath my estate to the following beneficiaries in the proportions specified:</p>
        <ul>
          <li>[BENEFICIARY NAME] - [PERCENTAGE OR SPECIFIC BEQUEST]</li>
          <li>[BENEFICIARY NAME] - [PERCENTAGE OR SPECIFIC BEQUEST]</li>
        </ul>
        <p>If any named beneficiary predeceases me or is unable to take their bequest, their share shall be distributed as follows:</p>
        <ul>
          <li>To their descendants per stirpes, if any; or</li>
          <li>To the remaining beneficiaries in proportion to their respective shares.</li>
        </ul>
        <p>All bequests are subject to the payment of debts, expenses, and taxes of my estate.</p>
      `,
      'revocation': `
        <h3>Article I - Revocation of Prior Wills</h3>
        <p>I hereby revoke all former wills, codicils, and testamentary dispositions of every kind whatsoever by me at any time heretofore made.</p>
        <p>This Will supersedes and replaces any and all previous testamentary documents, whether written or oral, that I may have executed prior to the date of this Will.</p>
      `,
      'guardianship': `
        <h3>Article - Guardianship of Minor Children</h3>
        <p>If at the time of my death any of my children are minors, I appoint [GUARDIAN NAME] as the guardian of the person and property of such minor children.</p>
        <p>If [GUARDIAN NAME] is unable or unwilling to serve as guardian, I appoint [ALTERNATE GUARDIAN NAME] as the alternate guardian.</p>
        <p>I direct that no bond shall be required of any guardian appointed herein unless required by law.</p>
        <p>I request that the guardian raise my children in accordance with my values and beliefs, and provide them with educational opportunities consistent with their abilities and interests.</p>
      `
    };
    
    // Find the most relevant template or generate custom content
    const topicLower = topic.toLowerCase();
    let generatedText = '';
    
    for (const [key, template] of Object.entries(legalTemplates)) {
      if (topicLower.includes(key)) {
        generatedText = template;
        break;
      }
    }
    
    // If no specific template found, generate general legal language
    if (!generatedText) {
      generatedText = `
        <h3>Article - ${topic}</h3>
        <p>Regarding ${topic}, I hereby declare and direct as follows:</p>
        <p>${context}</p>
        <p>This provision shall be interpreted and enforced in accordance with applicable law, and any ambiguities shall be resolved in favor of effectuating my intent as expressed herein.</p>
      `;
    }
    
    return { 
      generatedLegalText: generatedText,
      topic,
      jurisdiction,
      timestamp: new Date().toISOString()
    };
  },
});

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  
  try {
    const { prompt, context } = await readBody(event);

    if (!prompt) {
      throw createError({ 
        statusCode: 400, 
        statusMessage: 'Prompt is required' 
      });
    }

    // Check if we have a Pica API key, otherwise use OpenAI
    const apiKey = config.picaApiKey || config.openaiApiKey;
    
    if (!apiKey) {
      throw createError({ 
        statusCode: 500, 
        statusMessage: 'No AI API key configured' 
      });
    }

    const result = await generateText({
      model: openai('gpt-4o', {
        apiKey: apiKey,
      }),
      prompt: prompt,
      tools: {
        picaOneTool: picaOneTool,
      },
      system: `You are an AI assistant specialized in drafting legal language for digital wills and testaments. 

When users ask for help with:
- Legal clauses or will sections
- Formal legal language
- Specific will components (executor appointment, beneficiary designation, digital assets, etc.)
- Legal terminology or phrasing

Use the 'picaOneTool' to provide accurate, legally sound text that follows standard will drafting conventions.

For general questions or non-legal requests, respond conversationally with helpful guidance.

Always remind users that while you provide legally-informed templates, they should consult with a qualified attorney in their jurisdiction for final review and to ensure compliance with local laws.

Context about the user's current will: ${context || 'No additional context provided'}`,
      maxToolRoundtrips: 2,
    });

    return { 
      response: result.text,
      usage: result.usage,
      finishReason: result.finishReason
    };

  } catch (error: any) {
    console.error('Error generating AI response with Pica OneTool:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to generate AI response',
    });
  }
});