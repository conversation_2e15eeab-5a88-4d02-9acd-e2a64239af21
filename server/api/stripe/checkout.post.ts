import Stripe from 'stripe'
import { defineEventHandler, readBody } from 'h3'

const stripe = new Stripe(process.env.STRIPE_SECRET_TEST_KEY!, {
  apiVersion: '2025-08-28.basil',
})

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { priceId } = body

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'payment',
    success_url: 'http://localhost:3000/success',
    cancel_url: 'http://localhost:3000/cancel',
  })

  return { sessionId: session.id }
})