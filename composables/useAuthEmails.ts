import { useEmailService } from '~/utils/emailService'

export interface AuthEmailOptions {
  sendWelcomeEmail?: boolean
  customWelcomeMessage?: string
  includeConfirmationUrl?: boolean
}

/**
 * Composable for handling authentication-related emails
 */
export function useAuthEmails() {
  const emailService = useEmailService()

  /**
   * Send welcome email after successful signup
   */
  const sendWelcomeAfterSignup = async (
    userEmail: string,
    userName: string,
    options: AuthEmailOptions = {}
  ) => {
    const {
      sendWelcomeEmail = true,
      includeConfirmationUrl = false
    } = options

    if (!sendWelcomeEmail) {
      return { success: true, message: 'Welcome email disabled' }
    }

    try {
      // Generate confirmation URL if needed
      let confirmationUrl: string | undefined
      if (includeConfirmationUrl) {
        const baseUrl = process.client ? window.location.origin : 'https://volnt.xyz'
        confirmationUrl = `${baseUrl}/auth/confirm`
      }

      const result = await emailService.sendWelcomeSignupEmail(
        userEmail,
        userName,
        confirmationUrl
      )

      if (result.success) {
        console.log('Welcome email sent successfully to:', userEmail)
        return {
          success: true,
          message: 'Welcome email sent successfully',
          data: result.data
        }
      } else {
        console.error('Failed to send welcome email:', result.error)
        return {
          success: false,
          message: 'Failed to send welcome email',
          error: result.error
        }
      }
    } catch (error) {
      console.error('Error in sendWelcomeAfterSignup:', error)
      return {
        success: false,
        message: 'Error sending welcome email',
        error
      }
    }
  }

  /**
   * Send email confirmation reminder
   */
  const sendConfirmationReminder = async (
    userEmail: string,
    userName: string,
    confirmationUrl: string
  ) => {
    try {
      const result = await emailService.sendWelcomeSignupEmail(
        userEmail,
        userName,
        confirmationUrl
      )

      return result
    } catch (error) {
      console.error('Error sending confirmation reminder:', error)
      return { success: false, error }
    }
  }

  /**
   * Helper to extract user name from Supabase user object
   */
  const extractUserName = (user: any): string => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
    }
    
    if (user?.user_metadata?.first_name && user?.user_metadata?.last_name) {
      return `${user.user_metadata.first_name} ${user.user_metadata.last_name}`
    }
    
    if (user?.user_metadata?.first_name) {
      return user.user_metadata.first_name
    }
    
    if (user?.email) {
      // Extract name from email (before @)
      const emailName = user.email.split('@')[0]
      return emailName.charAt(0).toUpperCase() + emailName.slice(1)
    }
    
    return 'User'
  }

  /**
   * Auto-send welcome email for new signups
   * Call this after successful user registration
   */
  const handleNewUserSignup = async (
    user: any,
    options: AuthEmailOptions = {}
  ) => {
    if (!user?.email) {
      console.warn('No email found for user, skipping welcome email')
      return { success: false, message: 'No email found' }
    }

    const userName = extractUserName(user)
    const userEmail = user.email

    // Check if email is confirmed
    const needsConfirmation = !user.email_confirmed_at
    const emailOptions = {
      ...options,
      includeConfirmationUrl: needsConfirmation
    }

    return await sendWelcomeAfterSignup(userEmail, userName, emailOptions)
  }

  return {
    sendWelcomeAfterSignup,
    sendConfirmationReminder,
    extractUserName,
    handleNewUserSignup
  }
}
